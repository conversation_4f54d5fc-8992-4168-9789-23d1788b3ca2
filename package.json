{"name": "daakia-vc-sdk", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "webpack --mode=development"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@ant-design/icons": "^4.7.0", "@datadog/browser-logs": "^5.29.1", "@datadog/browser-rum": "^5.32.0", "@excalidraw/excalidraw": "^0.17.6", "@livekit/components-core": "^0.10.2", "@livekit/components-react": "^2.3.1", "@livekit/components-styles": "^1.0.12", "@livekit/react-core": "^1.1.0", "@livekit/track-processors": "^0.3.2", "antd": "^4.23.4", "apexcharts": "^3.49.2", "axios": "^0.27.2", "browser-fs-access": "^0.35.0", "chart.js": "^3.9.1", "clsx": "^2.1.1", "crypto-js": "^4.1.1", "css-loader": "^7.1.2", "dotenv-webpack": "^8.1.0", "file-loader": "^6.2.0", "is-electron": "^2.2.2", "json-loader": "^0.5.7", "livekit-client": "^2.2.0", "lottie-react": "^2.4.0", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-pdf": "^9.1.1", "react-router-dom": "^6.3.0", "sass-loader": "^16.0.0", "socket.io-client": "^4.8.1", "style-loader": "^4.0.0", "webpack-cli": "^5.1.4"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "@svgr/webpack": "^8.1.0", "babel-loader": "^9.1.3", "sass": "^1.83.0", "url-loader": "^4.1.1", "webpack": "^5.93.0"}}