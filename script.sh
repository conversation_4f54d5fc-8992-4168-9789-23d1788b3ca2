#!/bin/bash

# Function to increment the version
increment_version() {
  local version=$1
  local level=$2
  IFS='.' read -r major minor patch <<<"$(echo "${version#v}" | sed 's/-dev//')"  # Remove 'v' and '-dev'

  case $level in
    1) ((patch++)) ;;  # Patch update
    2) ((minor++)); patch=0 ;;  # Minor update
    3) ((major++)); minor=0; patch=0 ;;  # Major update
    *) echo "Invalid option"; exit 1 ;;
  esac

  echo "v$major.$minor.$patch-dev"  # Return the new version
}

# Fetch all tags and update
git fetch --tags

# Get the latest tag sorted by version
recent_tag=$(git tag --sort=-v:refname | head -n 1)

# If no tag exists, start with v0.0.0
if [ -z "$recent_tag" ]; then
  recent_tag="v0.0.0"
fi

echo "Current version: $recent_tag"

# Display options for the user
echo "Select the level of version increment:"
echo "1. Patch (e.g., v1.0.0 -> v1.0.1)"
echo "2. Minor (e.g., v1.0.0 -> v1.1.0)"
echo "3. Major (e.g., v1.0.0 -> v2.0.0)"

# Read user input
read -p "Enter your choice (1/2/3): " choice

# Calculate the new tag
new_tag=$(increment_version "$recent_tag" "$choice")

# Prompt for commit message
read -p "Enter commit message: " commit_msg

# Remove old build
rm -rf dist

# Build the project
yarn build
if [ $? -ne 0 ]; then
  echo "❌ Build failed. Aborting."
  exit 1
fi

# Add changes to git
git add .

# Commit changes with the user-provided commit message
git commit -m "UPDATE:: $commit_msg"

# Get the current working branch dynamically
current_branch=$(git rev-parse --abbrev-ref HEAD)

# Push the changes to the current branch
git push origin "$current_branch"

# Tag the commit with the new tag
git tag "$new_tag"

# Push the tag to the origin
git push origin "$new_tag"

# Create a ZIP file of the dist folder
zip_file="dist-${new_tag}.zip"
zip -r "$zip_file" dist

echo "✅ Successfully updated to version $new_tag with commit message: \"$commit_msg\""
