const path = require('path');
const Dotenv = require('dotenv-webpack');

module.exports = {
  mode: 'development', // or 'production' based on your environment
  entry: './src/index.js', // Your main entry file
  output: {
    path: path.resolve(__dirname, 'dist'), // Output directory
    filename: 'index.js', // Output file name
    library: 'daakia-vc-sdk', // Replace with your library's name
    libraryTarget: 'umd', // Export the library in a universal format
    globalObject: 'this', // Ensures compatibility with Node.js
  },
  resolve: {
    alias: {
      '@assets': path.resolve(__dirname, 'assets'),
    },
  },  
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: 'babel-loader', // Use Babel to transpile JavaScript files
      },
      {
        test: /\.scss$/,
        use: [
          'style-loader', // Injects CSS into the DOM
          'css-loader',   // Turns CSS into CommonJS
          'sass-loader',  // Compiles Sass to CSS
        ],
      },
      {
        test: /\.css$/,
        use: [
          'style-loader', // Injects CSS into the DOM
          'css-loader',   // Turns CSS into CommonJS
        ],
      },
      {
        test: /\.svg$/,
        use: [
          {
            loader: '@svgr/webpack',
            options: {
              icon: true,
            },
          },
          'file-loader',
        ],
      },
      {
        test: /\.(mp3|wav|ogg)$/, // Regex to handle audio files
        use: [
          {
            loader: 'file-loader',
            options: {
              name: '[name].[ext]', // Maintain original name
              outputPath: 'assets/sounds/', // Output under dist/assets/sounds/
              publicPath: 'assets/sounds/', // Correct public path for loading
            },
          },
        ],
      },
      {
        test: /\.(png|jpe?g|gif)$/i, // Match png, jpg, jpeg, and gif images
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 8192, // Convert images smaller than 8kb to base64
              name: '[path][name].[ext]', // Preserve the original folder structure
              outputPath: 'assets/images/', // Output directory for images
              publicPath: 'assets/images/', // Public URL for accessing images
            },
          },
        ],
      },
    ],
  },
  plugins: [
    new Dotenv(), // This will load your .env file and make variables available
  ],
  externals: {
    react: 'react',
    'react-dom': 'react-dom',
  },
};
 