import React from 'react';

/**
 * <PERSON>lones a single child element, applying additional props and a key.
 *
 * @param {React.ReactNode | React.ReactNode[]} children - The child element or elements to clone.
 * @param {Object} [props={}] - The props to apply to the cloned child.
 * @param {any} [key] - The key to apply to the cloned child.
 * @returns {React.ReactNode | React.ReactNode[]} The cloned child or children with the new props and key.
 */
export function cloneSingleChild(children, key,props = {}) {
  return React.Children.map(children, (child) => {
    // Checking if child is a valid React element and if only one child is provided.
    if (React.isValidElement(child) && React.Children.count(children) === 1) {
      return React.cloneElement(child, { ...props, key });
    }
    return child;
  });
}
