@use "../../styles/variables" as *;
// PrivateChats.scss

.private-chats-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: space-between;
  .lk-chat {
    max-height: 80%;
    .lk-message-body {
      margin: 0 !important;
    }
    &form {
      border: none;
    }
    &-form-outer {
      display: flex;
      align-items: center;
      border: none !important;
      // flex-direction: column;
      width: 100%;
    }
    &-form-buttons {
      bottom: 9px;
      .send-btn {
        position: static;
      }
    }
  }
  &-info {
    font-size: 10px;
    display: flex;
    flex-direction: column;
    height: auto;
    span {
      display: flex;
      justify-content: center;
      font-family: $font;

      @media screen and (min-height: 800px) {
        font-size: 12px;
      }
    }
  }
}

.private-chats-scrollable {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  overflow-x: auto;
  white-space: nowrap;
  min-height: 5rem;
  height: auto;
  gap: 5%;

  // Force scrollbar visibility
  scrollbar-color: grey transparent; // Color for Firefox
  scrollbar-width: thin; // Width for Firefox

  &::-webkit-scrollbar {
    height: 6px; // Set height for horizontal scrollbar
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1; // Light background color for the scrollbar track
  }

  &::-webkit-scrollbar-thumb {
    background-color: grey; // Grey color for the scrollbar thumb
    border-radius: 10px; // Rounded corners for the thumb
    border: 2px solid transparent; // Adds some padding around the thumb
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555; // Darker color on hover
  }

  .private-chat-participant {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-self: center;
    // width: 100px; // Width of each participant avatar
    // margin-right: 10px; // Spacing between avatars
    align-items: center;
    .lk-participant-name {
      color: white;
      font-family: $font;
      
      &-selected {
        font-family: $font;
        color: #9747ff;
      }
    }
  }
}

.no-participants {
  color: white;
  text-align: center;
  font-family: $font;
  font-weight: 300;
}

@media screen and (max-height: 743px) {
  .private-chats-container .lk-chat {
    max-height: 70%;
  }
}