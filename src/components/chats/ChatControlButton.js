// import { ChatToggle } from "@livekit/components-react";
import React from "react";
import { DrawerState } from "../../utils/constants";
import { ReactComponent as ChatIcon } from "../../assets/icons/ChatIcon.svg";

export default function ChatControlButton({
  showChatDrawer,
  setShowChatDrawer,
  setDrawerState,
  showIcon = true,
  showText = false,
}) {
  return (
    <div
      className="lk-button control-bar-button-icon control-bar-button chat-icon"
      onClick={() => {
        setShowChatDrawer(!showChatDrawer);
        setDrawerState(DrawerState.CHAT);
      }}
    >
      {showIcon && <ChatIcon />}
      {showText && "Chat"}
    </div>
  );
}
