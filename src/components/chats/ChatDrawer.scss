@use "../../styles/variables" as *;

.chat-drawer {
  &.hide {
    display: none;
  }
  &.show {
    display: block;
  }
  @media screen and (max-width: 450px) {
    padding: 0;
  }

  .chat-close-ico{
    display: flex;
    align-items: flex-start;
    position: absolute;
    right: 0;
  }

  .sd-header{
    border: none !important;
    width: 100%;
    position: relative;
  }
  .sd-container-inner {
    justify-content: space-between;
    .sd-container-below{
      height: 100%;
      overflow: hidden;
      padding: 0;
      position: relative;
    }
    .sd-title {
      display: flex;
      margin-bottom: 0.5rem;
      justify-content: flex-start !important;
      .disabled-private-chat{
        width: 100% !important;
      }
      span {
        width: 60%;
        text-align: center;
        border-bottom: 1px solid white;
        font-weight: 300;
        cursor: pointer;
        font-size: 16px;
        padding: 0.3rem 0;
        margin-top: 0.2rem;
        &:nth-child(1){
          width: 40%;
        }
      }
      .active {
        border-bottom: 2px solid #0a84ff !important;
        color: #0a84ff !important;
        font-weight: 500;
        transition: all 0.2s;

      }
    }
  }
  .lk-chat {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    border: none;
    position: relative;
    &-image-preview{
      width: -webkit-fill-available;
    }
    &-body-inner{
      display: flex;
      flex-direction: column;
      gap: 0.2rem;
      background-color: #1b1b1b;
      padding: 0.4rem;
      border-radius: 4px;
      border-left: 3px solid #bbbbbb;
      &-avtar{
        display: flex;
        gap: 0.5rem;
        align-items: center;
        span{
          font-size: 12px;
        }
      }
    }
    &-pdf{
      position: relative;
      height: auto;
      text-align: left;
    }
    .reply-essage-in-progress{
      margin-bottom: 3rem;
    }
    &-reply{
      display: flex;
      flex-direction: column;
      background-color: #323437;
      padding: 0.3rem;
      border-radius: 4px;
      // box-shadow: ;
      &-image{
        display: flex;
        gap: 0.2rem;
        background-color: #323437;
        border-radius: 4px;
        padding: 0.2rem;
        font-size: 12px;
        align-items: center;
        // flex-direction: column;
        &-box{
          display: flex;
          gap: 0.3rem;
          div{
            color: black;
          }
        }
        svg{
          width: 16px;
          height: 16px;
        }
        span{
          display: flex;
          padding: 0 0.2rem;
        }
      }
      div{
        display: flex;
        gap: 0.1rem;
        align-items: center;
        span{
          font-size: 11px;
          color: #1a1f25;
        }
      }
      &-message{
        // margin-left: 1.4rem;
        font-size: 12px;
      }
    }
    &-media{
      position: relative;
      overflow: hidden;
      margin-bottom: 0.1rem;
      img{
        width: 100%;
        height: auto;
      }
      &-pdf{
        position: relative;
      }
      &-message-show{
        display: block;
        margin: 0;
      }
      &-message-hide{
        display: none;
      }
      &-overlay{
        position: absolute;
        bottom: 0px;
        left: 0;
        background-color: #000000ab;
        width: 102%;
        height: 3rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        justify-content: space-between;
        padding: 0.5rem;
        .lk-chat-pdf-download{
          svg{
            width: 20px;
            height: 20px;
          }
        }
      }
    }
    &-pdf-preview{
      width: 12.2rem;
      height: auto;
      max-height: 9rem;
      position: relative;
      overflow: hidden;
    }
    &-file-uploaded{
      margin-bottom: 3.5rem !important;
    }
    &-messages {
      gap: 0.25rem;
      margin-bottom: 0.5rem;
      padding-right: 0.2rem;
      &-private{
        .lk-chat-receiver{
          margin: 0;
        }
      }
      &::-webkit-scrollbar{
        display: block;
        width: 7px !important;
      }
      &::-webkit-scrollbar-thumb{
        background-color: #798593;
        border-radius: 0.25rem;
      }
      &::-webkit-scrollbar-track{
        background-color: #000;
      }
      .lk-chat-entry {
        margin: 0;
        gap: 0;
        height: auto;
        .lk-meta-data{
          margin-top: 1rem;
          padding: 0;
          &-name{
            display: flex;
            gap: 0.5rem;
            .lk-participant-name{
              margin: 0;
              font-weight: 500;
              color: white;
              display: flex;
              align-items: flex-end;
              margin-bottom: 5px;
              font-family: $font;
            }
          }
        }
        .lk-message-outer{
          display: flex;
          flex-direction: column;
          height: auto;
          // width: fit-content;
          width: auto;
          .lk-message-body{
            margin-left: 2rem;
            border-radius: 0px 6px 6px 6px;
            max-width: 75%;
            cursor: default;
            background-color: #4A5767;
            font-family: $font;
            font-size: 14px;
            position: relative;
            display: flex;
            // align-items: center;   //dont do this
            gap: 0.5rem;
            height: auto;
            padding: 0.3rem 0.7rem;
            &-replied-message{
              background-color: #323437 !important;
            }
          }
          .lk-timestamp{
            margin-left: 2rem;
            font-size: 10px;
            color: #BFBFBF;
            max-width: 80%;
            text-align: right;
          }
        }
      }
      .lk-local{
        align-items: flex-end;
        margin-right: 0.3rem;
        .lk-message-outer{
          display: flex;
          align-items: flex-end;
          // width: fit-content;
          width: -webkit-fill-available;
          position: relative;
          overflow: hidden;
          @-moz-document url-prefix() {
            width: -moz-available;
          }
          .lk-timestamp{
            width: -webkit-fill-available;
            text-align: left;
          }
          .lk-message-body{
            background-color: #131B26;
            border-radius: 6px 0px 6px 6px;
            font-weight: 350;
            font-family: $font;
            font-size: 14px;
            max-height: 13rem;
            position: relative;
            margin: 0;
            .lk-chat-body{
              &-outer{
                // overflow: hidden;
                height: 100%;
                width: 100%;
                position: relative;
                text-align: left;
              }
              &-inner{
                background-color: #1b1b1b;
                padding: 0.4rem;
                border-radius: 4px;
                display: flex;
                flex-direction: column;
                gap: 0.2rem;
                &-avtar{
                  display: flex;
                  gap: 0.5rem;
                  align-items: center;
                  span{
                    font-size: 12px;
                  }
                }
              }
            }
            .lk-preview-attatchment{
              display: none;
            }
            &-pdf{
              // overflow: hidden;
              display: flex;
              justify-content: center;
              overflow-x: hidden;
              position: relative;
              .lk-preview-attatchment{
                display: block;
                right: 0;
                z-index: 100;
              }
            }
            &-image{
              max-height: 40svh;
              position: relative;
              .lk-preview-attatchment{
                display: block;
                right: 0;
              }
            }
            .lk-chat-media{
              height: auto;
              width: 100%;
              overflow: hidden;
              position: relative;
              img{
                // width: -webkit-fill-available;
                height: 100%;
                width: 100%;
              }
              &-img{
                height: auto;
                overflow-y: auto;
                overflow-x: hidden;
                position: relative;
                img{
                  width: 100%;
                }
              }
            }
          }
          .file-uploaded{
            display: flex;
            justify-content: flex-end;
            margin-top: 0.2rem;
            img{
              width: 80%;
              border-radius: 6px 0px 6px 6px;
            }
          }
        }
      }
    }
    .file-uploaded{
      // flex-direction: column;
      // max-height: 185px;
      // overflow: auto;
      .lk-chat-form-outer{
        width: 100%;
        display: flex;
        flex-direction: column;
        // position: relative;
        .lk-chat-form-input{
          max-height: 104px;
        }
        .file-pre-upload{
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #2b2d2f;
          margin: 0.2rem;
          padding: 0.2rem;
          border-radius: 6px;
          &-box{
            display: flex;
            align-items: center;
            gap: 0.5rem;
            .file-upload{
              &-image{
                width: 30px;
                height: 30px;
              }
              &-right{
                display: flex;
                flex-direction: column;
                &-file-name{
                  font-size: 12px;
                  color: #e8eaeb;
                }
              }
            }
          }
        }
      }
      .lk-chat-form-buttons{
        position: relative;
        justify-content: flex-end;
      }
    }
    &-form {
      padding: 0;
      border: none;
      width: 100%;
      background-color: #354657 !important;
      border-radius: 6px;
      position: relative;
      max-height: 104px;
      display: flex;
      height: auto;
      position: sticky;
      border: 1px solid #798593;
      gap: 0;
      &-buttons{
        top: 0;
      }
      &-upload{
        height: 100%;
        width: 22px;
        align-items: center;
        position: relative;
        &-icon{
          width: 22px !important;
          height: 100% !important;
        }
      }
      &-outer{
        width: 100%;
        display: flex;
        .file-uplaod-right-subhead{
          display: flex;
          gap: 0.3rem;
          span{
            font-size: 10px !important;
            color: #A6A8AA;
          }
        }
        .reply-message{
          display: flex;
          align-items: flex-start;
          padding: 0.2rem 0.5rem;
          background-color: #323e4d;
          border-radius: 6px 6px 0px 0px;
          box-shadow: 0px 0px 5px 0px #00000099;
          gap: 0.3rem;
          position: absolute;
          width: 100%;
          top: -3.2rem;
          &-sender{
            font-size: 10px;
            width: 100%;
          }
          &-close{
            position: absolute;
            right: 0.5rem;
            top: 4px;
          }
          &-text{
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 100%;
            font-size: 14px;
            // background-color: #4a5767;
            border-radius: 6px;
            // box-shadow: 0px 0px 5px 0px #00000099;
            padding: 0.1rem;
            &-msg{
              margin-left: 1.2rem;
              background-color: #4a5767;
              border-radius: 4px;
              padding: 0.2rem;
              box-shadow: 0px 0px  5px 0px #00000099;
              width: -webkit-fill-available;
              span{
                font-size: 12px;
              }
            }
            &-image{
              display: flex;
              gap: 0.4rem;
            }
          }
        }
      }
      >div{
        .lk-chat-form-input{
          background-color: #354657 !important;
          border: none;
        }
        :first-child{
          display: flex;
          border: none;
          &::placeholder{
            color: rgb(164, 164, 164);
            font-weight: 300;
            font-family: $font;
          }
          svg{
            width: 16px;
            height: 16px;
          }
        }
      }
      .file-upload{
        background-color: #344756;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.3rem 1rem;
        width: 95%;
        margin: 0.5rem;
        border-radius: 4px;
        position: absolute;
        top: -4.1rem;
        &-success{
          top: -4.4rem;
        }
        &-right{
          display: flex;
          flex-direction: column;
          width: 80%;
        }
        &-end{
          display: flex;
          flex-direction: column;
          justify-content: center;
          height: 100%;
          width: auto;
          gap: 0.4rem;
          svg{
            width: 20px;
            height: 20px;
          }
          span{
            font-size: 10px;
            color: #A6A8AA;
          }
        }
        &-progress{
          display: flex;
          align-items: center;
          .ant-progress-inner{
            height: 6px;
          }
        }
        &-image{
          background-color: #EDF2FD;
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
        }
        &-first{
          display: flex;
          align-items: center;
          gap: 0.5rem;
          width: 100%;
          span{
            &:nth-child(1){
              font-size: 12px;
            }
            &:nth-child(2){
              font-size: 10px;
              color: #A6A8AA;
            }
          }
        }
        &-second{
          cursor: pointer;
        }
      }
      &-input {
        font-size: 14px;
        max-height: 104px;
        width: 100%;
        font-family: $font;
        padding: 8px;
        height: 41px;
        &:focus {
          outline: none;
        }
        
        @media screen and (max-width: 900px) {
          width: 95%;
        }
        &::-webkit-scrollbar {
          width: 6px;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #798593;
          border-radius: 0.25rem;
        }
        &::-webkit-scrollbar-track {
          background-color: #354657;
        }
      }
      &-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0.2rem;
        position: relative;
        .send-btn {
          position: static;
          padding: 0;
          border-radius: 0;
          padding: 0 0.4rem;
          padding-right: 0;
          background-color: transparent;
        }
      }
      &-button{
        display: flex;
        height: 100%;
        svg{
          width: 100%;
          height: 22px;
        }
      }
    }
  }
}
.lk{
  &-preview-attatchment{
    position:absolute;
    top: 0;
    background-color: #ffffff99;
    width: 1.5rem;
    padding: 0.1rem;
    border-radius: 2px;
    cursor: pointer;
  }
}
.attatchment-preview-image{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}
.lk-chat-pdf-page{
  min-height: 10rem !important;
  height: 5rem;
}
.lk-attatchment-preview{
  .attatchment-preview-modal-title{
    strong{
      color: white;
      font-family: $font;
    }
  }
  .ant-modal-body{
    padding: 0;
    background-color: transparent;
  }
  .ant-modal-content{
    background-color: transparent;
    border-radius: 10px;
    .ant-modal-close{
    position: fixed;
    .ant-modal-close-icon{
      color: white;
    }
    }
  }
  .ant-modal-header{
    background-color: #323437;
    border-radius: 10px 10px 0px 0px;
  }
  .attatchment-preview-modal-title{
    display: flex;
    gap: 1rem;
  }
  .attatchment{
    &-name{
      font-size: 12px;
      color: white;
      position: fixed;
      top: 1rem;
      left: 1rem;
      z-index: 100;
    }
    &-download{
      width: 25px;
      height: 25px;
      cursor: pointer;
      filter: invert(1);
      position: fixed;
      top: 1rem;
      right: 3rem;
    }
    &-zoom-in{
      width: 25px;
      height: 25px;
      cursor: pointer;
      filter: invert(1);
      position: fixed;
      top: 1rem;
      right: 6rem;
      z-index: 100;
    }
    &-zoom-out{
      width: 25px;
      height: 25px;
      cursor: pointer;
      filter: invert(1);
      position: fixed;
      top: 1rem;
      right: 9rem;
      z-index: 100;
    }
    &-zoom-reset{
      width: 25px;
      height: 25px;
      cursor: pointer;
      filter: invert(1);
      position: fixed;
      top: 1rem;
      right: 12rem;
      z-index: 100;
    }
  }
}
.lk-message-body-reply{
  padding: 0;
  margin-top: 1rem;
  @media screen and (max-width: 450px) {
    z-index: 100;
  }
  .ant-popover-inner{
    background: none;
    &-content{
      padding: 0;
    }
  }
  .ant-popover-arrow{
    display: none;
  }
  .lk-message-body-reply-icon{
    width: 25px;
    height: 25px;
  }
  .ant-popover-content{
    margin: 0 0.5rem;
  }
}
.lk-chat-body-outer{
  overflow: auto;
  overflow-x: hidden;
  width: -webkit-fill-available;
  height: 100%;
  &::-webkit-scrollbar{
    width: 5px !important;
  }
  &::-webkit-scrollbar-thumb{
    background-color: #798593;
    border-radius: 0.25rem;
  }
  &::-webkit-scrollbar-track{
    background-color: #3d444d;
  }
}
.sender-group{
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  position: relative;
  .lk-chat-sender{
    text-align: right;
    margin-right: 0.4rem;
  }
}
.lk-chat-pdf-download{
    width: 20px;
    height: 20px;
}
.private-chat-count{
  position: absolute;
  color: white;
  top: 0.3rem;
  right: 0.8rem;
  background-color: #0a84ff;
  display: flex;
  justify-content: center;
  align-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 12px;
}
.public-chat-count{
  position: absolute;
  color: white;
  top: 0.3rem;
  left: 30%;
  background-color: #0a84ff;
  display: flex;
  justify-content: center;
  align-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 12px;
}
.highlighted-message{
  position: relative;
  &-highlight{
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    z-index: 100;
    background-color: #0a84ff25;
  }
}
.lk-message-body-reply{
  padding: 0;
}