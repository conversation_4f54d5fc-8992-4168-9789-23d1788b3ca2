import React, { useState, useEffect } from "react";

function DownScaleImage ({ href, handlePreview }) {
  const [resizedSrc, setResizedSrc] = useState(null);

  const downscaleImage = (url, maxWidth, maxHeight) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = "Anonymous"; // To handle CORS
      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        let { width, height } = img;

        // Maintain aspect ratio
        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height;
          if (width > height) {
            width = maxWidth;
            height = maxWidth / aspectRatio;
          } else {
            height = maxHeight;
            width = maxHeight * aspectRatio;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw image to canvas
        ctx.drawImage(img, 0, 0, width, height);

        // Get the resized image as a base64 URL
        resolve(canvas.toDataURL("image/jpeg", 0.5)); // Adjust quality as needed
      };
      img.onerror = reject;
      img.src = url;
    });
  };

  useEffect(() => {
    if (href) {
      downscaleImage(href, 200, 200) // Set desired max width/height
        .then(setResizedSrc)
        .catch((err) => console.error("Error resizing image:", err));
    }
  }, [href]);

  return (
    <img
      src={resizedSrc || href} // Show resized image, fallback to original
      alt="Attachment Preview"
      className="lk-chat-image-preview"
      onClick={(e) => {
        e.stopPropagation();
        handlePreview();
      }}
    />
  );
};

export default DownScaleImage;
