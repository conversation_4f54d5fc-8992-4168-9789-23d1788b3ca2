import React from 'react';
import './Overlay.scss';
import { DataReceivedEvent } from '../../utils/constants';
import { ReactComponent as BigHeart } from "./icons/BigHeart.svg";
import { ReactComponent as BigBlush } from "./icons/BigBlush.svg";
import { ReactComponent as BigClap } from "./icons/BigClap.svg";
import { ReactComponent as BigSmile } from "./icons/BigSmile.svg";
import { ReactComponent as BigThumbsUp } from "./icons/BigThumbsUp.svg";

export function ReactionsOverlay({reaction}) {
  return (
    <div className="reaction-overlay float-animation">
      <div className="reaction">
      {
        reaction === DataReceivedEvent.HEART && <BigHeart/>
      }
      {
        reaction === DataReceivedEvent.BLUSH && <BigBlush/>
      }
      {
        reaction === DataReceivedEvent.CLAP && <BigClap/>
      }
      {
        reaction === DataReceivedEvent.SMILE && <BigSmile/>
      }
      {
        reaction === DataReceivedEvent.THUMBS_UP && <BigThumbsUp/>
      }
      {
        reaction === DataReceivedEvent.GRINNING_FACE && <BigSmile/>
      }
      </div>
    </div>
  )
};

