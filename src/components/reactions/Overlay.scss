.reaction-overlay {
  position: absolute;
  top: 0%;
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 100;
  justify-content: center;
  align-items: flex-end;
  pointer-events: none; /* Allows clicks to pass through to the underlying component */
}
.float-animation {
  animation: float 6s ease-in-out forwards;
  animation-iteration-count: infinite;
}

@keyframes float {
  0% {
    transform: translateY(100%);
    opacity: 1;
  }
  50% {
    transform: translateY(0) translateX(20px) rotate(15deg);
    opacity: 0.75;
  }
  75% {
    transform: translateY(-50%) translateX(-15px) rotate(-10deg);
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100%) translateX(-20px) rotate(-15deg);
    opacity: 0;
  }
}

.reaction {
  position: absolute;
  font-size: 40px;
  top: 0%;
}
