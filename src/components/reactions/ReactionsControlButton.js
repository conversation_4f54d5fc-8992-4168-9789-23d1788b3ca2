/* eslint-disable no-unused-vars */
import React, { useCallback, useState } from "react";
import { Popover, Row, Col } from "antd";
// import { publishAction } from "../../utils/reactions";
import { DataReceivedEvent } from "../../utils/constants";

import "../../styles/ControlBar.scss";
import "../../styles/Settings.scss";
import "../../styles/index.scss";
import "./Reactions.scss";

// import { ReactComponent as GriningFaceIcon } from "./icons/GriningFace.svg";
import { ReactComponent as ReactionIcon } from "./icons/ReactionIcon.svg";
import { ReactComponent as HeartIcon } from "./icons/Red-heart.svg";
import { ReactComponent as BlushIcon } from "./icons/Blush.svg";
import { ReactComponent as ClappingIcon } from "./icons/Clap.svg";
import { ReactComponent as SmileIcon } from "./icons/Smile.svg";
import { ReactComponent as ThumpsUpIcon } from "./icons/Thumbs-up.svg";

export function ReactionsControlButton({
  showEmojiReaction,
  setShowEmojiReaction,
}) {
  const [showPopover, setShowPopover] = useState(false);

  const handleReaction = useCallback((reaction) => {
    setShowEmojiReaction(reaction);
    setShowPopover(false);
  },[setShowEmojiReaction, setShowPopover])

  const content = (
    <Row gutter={[16, 16]} className="rc-parent-container">
      <Col
        xs={12}
        sm={8}
        md={8}
        lg={4}
        className="rc-icon-container"
        onClick={() => handleReaction(DataReceivedEvent.HEART)}
      >
        <HeartIcon />
      </Col>
      <Col
        xs={12}
        sm={8}
        md={8}
        lg={4}
        className="rc-icon-container"
        onClick={() => handleReaction(DataReceivedEvent.BLUSH)}
      >
        <BlushIcon />
      </Col>
      <Col
        xs={12}
        sm={8}
        md={8}
        lg={4}
        className="rc-icon-container"
        onClick={() => handleReaction(DataReceivedEvent.CLAP)}
      >
        <ClappingIcon />
      </Col>
      <Col
        xs={12}
        sm={8}
        md={8}
        lg={4}
        className="rc-icon-container"
        onClick={() => handleReaction(DataReceivedEvent.SMILE)}
      >
        <SmileIcon />
      </Col>
      <Col
        xs={12}
        sm={8}
        md={8}
        lg={4}
        className="rc-icon-container"
        onClick={() => handleReaction(DataReceivedEvent.THUMBS_UP)}
      >
        <ThumpsUpIcon />
      </Col>
    </Row>
  );

  return (
    <Popover
      content={content}
      title={null}
      trigger="click"
      open={showPopover}
      onOpenChange={() => setShowPopover(!showPopover)}
      overlayInnerStyle={{
        backgroundColor: "#1e1e1e",
      }}
      onClick={() => setShowPopover(!showPopover)}
    >
      <div
        className="lk-button control-bar-button-icon control-bar-button"
      >
        <ReactionIcon />
      </div>
    </Popover>
  );
}
