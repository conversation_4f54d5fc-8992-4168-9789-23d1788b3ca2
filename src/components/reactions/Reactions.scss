.rc-parent-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: auto; /* Ensure it uses full width */
}

.rc-icon-container {
  display: flex !important;
  justify-content: center;
  align-items: center;
  height: auto;
  cursor: pointer;
  filter: brightness(0.7);
  transition: all 0.2s ease-in;
  &:hover{
    filter: brightness(1);
    scale: 1.3;
    animation: emoji-float 0.7s infinite;
  }
}
@keyframes emoji-float {
  0%,100%{
    // transform: translateX(-2px) translateY(-2px);
    rotate: 10deg;
  }
  50%{
    // transform: translateX(2px) translateY(2px);
    rotate: -10deg;
  }
}

.rc-icon-container svg {
  width: 30px;
  height: 30px;
}
