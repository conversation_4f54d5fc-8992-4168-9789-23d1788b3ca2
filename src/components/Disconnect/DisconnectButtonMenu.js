/* eslint-disable no-unused-vars */
import React from "react";
import { Popover } from "antd";
import { DisconnectButton } from "@livekit/components-react";
import { ReactComponent as LeaveIcon } from "../../assets/icons/LeaveIcon.svg";

import { Endpoints } from "../../API/Endpoints/routes";
import { APIrequest } from "../../API/axios";

import "../../styles/ControlBar.scss";
import "../../styles/index.scss";
import "./DisconnectButtonMenu.scss";

export function DisconnectButtonMenu({
  id,
  showPopover,
  setShowPopover,
  coHostToken,
  isElectronApp,
  isHost,
  apiToken,
  devMode
}) {
  const endMeeing = async () => {
    try {
      const response = await APIrequest({
        method: Endpoints.end_meeting.method,
        endpoint: Endpoints.end_meeting.url,
        payload: {
          meeting_uid: id,
        },
        token: isHost ? apiToken : coHostToken,
        devmode: devMode,
      });
      return response;
    } catch (error) {
      console.log("Error Ending Meeting: ", error);
    }
  };

  const content = (
    <div className="primary-font db-parent">
      <div className="db-button-end-call" onClick={() => {endMeeing()
        if (isElectronApp) {
          window?.electronAPI?.ipcRenderer?.send("stop-annotation");
        }}
      }>
        End Call
      </div>
      <DisconnectButton
        style={{ backgroundColor: "#D9D9D9" }}
        className="db-button-leave-call"
        onClick={() => {
          if (isElectronApp) {
            window?.electronAPI?.ipcRenderer?.send("stop-annotation");
          }
        }}
      >
        Leave Call
      </DisconnectButton>
    </div>
  );

  return (
    <Popover
      content={content}
      title={null}
      trigger="click"
      open={showPopover}
      onOpenChange={() => setShowPopover(!showPopover)}
      overlayInnerStyle={{
        backgroundColor: "#000000",
        borderRadius: "8px",
      }}
      overlayClassName="end-meeting-mobile-view"
    >
      <div
        // onClick={() => setShowPopover(!showPopover)}
        className=" lk-button control-bar-button control-bar-button-icon"
        style={{ backgroundColor: "#FF3B30" }}
      >
        <LeaveIcon />
      </div>
    </Popover>
  );
}
