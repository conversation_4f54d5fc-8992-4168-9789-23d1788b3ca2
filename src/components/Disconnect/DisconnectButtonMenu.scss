.db-parent {
  width: 11vw;
}

.db-button-end-call {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4% 6%;
  cursor: pointer;
  width: 100%;
  background-color: rgba(255, 59, 48, 1);
  border-radius: 7px;
  color: white;
  font-size: 0.8rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.db-button-leave-call {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4% 6%;
  cursor: pointer;
  width: 100%;
  background-color: rgba(239, 241, 244, 1);
  border-radius: 7px;
  color: rgba(36, 36, 36, 1);
  font-size: 0.8rem;
  font-weight: 700;
}

.db-button-leave-call :hover {
  background-color: rgba(239, 241, 244, 1) !important;
}

@media (max-width: 950px) {
  .db-parent {
    width: 12vw; /* Adjust width as needed */
  }

  .db-button-end-call,
  .db-button-leave-call {
    font-size: 0.7rem; /* Reduce font size */
    padding: 3% 5%; /* Adjust padding as needed */
    border-radius: 5px;
  }
}
@media screen and (max-width: 450px) {
  .end-meeting-mobile-view{
    .ant-popover-inner-content{
      width: 120px;
      .db-parent{
        width: 100% !important;
      }
    }
  }
}