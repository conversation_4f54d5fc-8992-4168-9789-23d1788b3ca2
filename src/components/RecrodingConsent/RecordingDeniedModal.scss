.recording-denied-modal {
    .ant-modal-content {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ffffff;
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #E54D4D;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 600px;

        .denied-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            

            .icon-container {
                margin-bottom: 12px;
                
                .anticon {
                    font-size: 30px;
                    color: #E54D4D;
                }
            }

            .denied-title {
                color: #E54D4D;
                font-size: 16px;
                line-height: 1.2;
                margin: 0 0 8px 0;
                font-weight: 500;
            }

            .denied-message {
                color: #303030;
                font-size: 14px;
                line-height: 1.2;
                margin: 0 0 16px 0;
                max-width: 100%;
            }

            .button-container {
                display: flex;
                gap: 12px;
      
          

                .ant-btn {
                    height: 36px;
                    min-width: 100px;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: none;
                }

                .change-button {
                    background: transparent;
                    border: 1px solid #4F6EF7;
                    color: #4F6EF7;

                    &:hover {
                        background: rgba(79, 110, 247, 0.1);
                    }

                    &:focus {
                        border-color: #4F6EF7;
                        color: #4F6EF7;
                    }
                }

                .dismiss-button {
                    background: transparent;
                    border: 1px solid #E54D4D;
                    color: #E54D4D;

                    &:hover {
                        background: rgba(229, 77, 77, 0.1);
                    }

                    &:focus {
                        border-color: #E54D4D;
                        color: #E54D4D;
                    }
                }
            }
        }
    }

    .ant-modal-mask {
        background-color: rgba(0, 0, 0, 0.85) !important;
    }
}

.close-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background: transparent;
    border: none;
    color: #E54D4D;
    font-size: 14px;
    &:hover {
        color: #FF6B6B;
    }
} 