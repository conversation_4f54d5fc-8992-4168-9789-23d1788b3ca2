import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { InfoCircleFilled } from "@ant-design/icons";
import { DataReceivedEvent } from "../../utils/constants";
import { RecordingDeniedModal } from "./RecordingDeniedModal";
import "./RecordingConsentModal.scss";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import { parseMetadata } from "../../utils/helper";

export function RecordingConsentModal({
  isRecordingConsentModalOpen,
  setIsRecordingConsentModalOpen,
  remoteParticipant,
  room,
  id,
  setToastNotification,
  setToastStatus,
  setShowToast,
  showDeniedModal,
  setShowDeniedModal,
  participantConsent,
  setParticipantConsent,
  devMode,
}) {
  const [moderatorIds, setModeratorIds] = useState([]);

  useEffect(() => {
    // Get the moderator ids from the remote participant
    const moderatorIdentities = [];

    // Iterate over remoteParticipant object
    Object.entries(remoteParticipant).forEach(([identity, participant]) => {
      const metadata = parseMetadata(participant.metadata);
      if (
        metadata?.role_name === "moderator" ||
        metadata?.role_name === "cohost"
      ) {
        moderatorIdentities.push(identity);
      }
    });

    setModeratorIds(moderatorIdentities);
  }, [remoteParticipant]);

  const handleAgree = async () => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.RECORDING_CONSENT_STATUS,
        consent: "accept",
      })
    );
    room.localParticipant.publishData(data, {
      reliable: true,
      destinationIdentities: moderatorIds,
    });
    setIsRecordingConsentModalOpen(false);
    setShowDeniedModal(false);
    try {
      const metadata = parseMetadata(room?.localParticipant?.metadata);
      const meetingSessionResponse = await SettingsMenuServices.getMeetingSession(id, devMode);
      if(meetingSessionResponse.success === 0){
        setToastNotification(meetingSessionResponse.message);
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      const response = await SettingsMenuServices.updateParticipantConsent(
        id,
        metadata?.current_session_uid?.toString() || meetingSessionResponse?.data?.id.toString(),
        room?.localParticipant?.participantInfo,
        metadata?.meeting_attendance_id.toString(),
        true,
        devMode
      );
      if (response.success === 0) {
        setToastNotification(response.message);
        setToastStatus("error");
        setShowToast(true);
      } else {
        if (response?.data?.can_start_recording) {
          const recordingResponse = await SettingsMenuServices.startCloudRecording(
            id,
            null,
            room?.localParticipant?.participantInfo,
            devMode
          );

          if (recordingResponse.success === 0) {
            setToastNotification("Error in starting recording");
            setToastStatus("error");
            setShowToast(true);
            return;
          }
        }
        // update the participant consent of ownself
        setParticipantConsent(participantConsent.map(participant => participant.participantId === room?.localParticipant?.identity ? {
          ...participant,
          consent: "accept",
        } : participant));
        setToastNotification("Consent granted");
        setToastStatus("success");
        setShowToast(true);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  const handleDisagree = async () => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.RECORDING_CONSENT_STATUS,
        consent: "reject",
      })
    );
    room.localParticipant.publishData(data, {
      reliable: true,
      destinationIdentities: moderatorIds,
    });
    setIsRecordingConsentModalOpen(false);
    setShowDeniedModal(true);
    try {
      const metadata = parseMetadata(room?.localParticipant?.metadata);
      const meetingSessionResponse = await SettingsMenuServices.getMeetingSession(id, devMode);
      if(meetingSessionResponse.success === 0){
        setToastNotification(meetingSessionResponse.message);
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      const response = await SettingsMenuServices.updateParticipantConsent(
        id,
        metadata?.current_session_uid?.toString() || meetingSessionResponse?.data?.id.toString(),
        room?.localParticipant,
        metadata?.meeting_attendance_id.toString(),
        false,
        devMode
      );
      if (response.success === 0) {
        setToastNotification(response.message);
        setToastStatus("error");
        setShowToast(true);
      } else {
        // update the participant consent of ownself
        setParticipantConsent(participantConsent.map(participant => participant.participantId === room?.localParticipant?.identity ? {
          ...participant,
          consent: "reject",
        } : participant));
        setToastNotification("Consent denied");
        setToastStatus("success");
        setShowToast(true);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  const handleDeniedModalClose = () => {
    setShowDeniedModal(false);
  };

  return (
    <>
      <Modal
        title={null}
        open={isRecordingConsentModalOpen}
        onCancel={() => setIsRecordingConsentModalOpen(false)}
        footer={null}
        closable={false}
        className="recording-consent-modal"
        width={700}
        maskStyle={{ backgroundColor: "rgba(0, 0, 0, 0.85)" }}
      >
        <div className="consent-content">
          <div className="message-container">
            <div className="icon-container">
              <InfoCircleFilled />
            </div>
            {/* check in the css there is part of the message */}
            <h2 className="consent-message">
              The host wants to record this meeting and requests
            </h2>
          </div>
          <div className="button-container">
            <Button className="agree-button" onClick={handleAgree}>
              Agree
            </Button>
            <Button className="deny-button" onClick={handleDisagree}>
              Deny
            </Button>
          </div>
        </div>
      </Modal>

      <RecordingDeniedModal
        isOpen={showDeniedModal}
        onClose={handleDeniedModalClose}
        onChangeToAgree={() => {
          setShowDeniedModal(false);
          handleAgree();
        }}
      />
    </>
  );
}
