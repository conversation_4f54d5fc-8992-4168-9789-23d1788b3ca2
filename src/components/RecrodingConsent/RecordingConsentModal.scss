.recording-consent-modal {
    .ant-modal-content {
        background: #ffffff;
        border-radius: 16px;
        padding: 40px;
        border: 1px solid #4F6EF7;
     

        .consent-content {
            display: flex;
            flex-direction: column;
            align-items: center;

            .message-container {
                display: flex;
                align-items: flex-start;
                gap: 16px;
                margin-bottom: 40px;
                width: 100%;

                .icon-container {
                    margin-top: 4px;
                    
                    .anticon {
                        font-size: 40px;
                        color: #4F6EF7;
                    }
                }

                .consent-message {
                    color: #303030;
                    font-size: 20px;
                    line-height: 1.4;
                    margin: 0;
                    font-weight: normal;
                    flex: 1;
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    &::after {
                        content: "your consent.";
                        display: block;
                        margin-top: 4px;
                    }
                }
            }

            .button-container {
                display: flex;
                gap: 20px;

                .ant-btn {
                    height: 44px;
                    min-width: 120px;
                    border-radius: 6px;
                    font-size: 16px;
                    font-weight: 500;
                    padding: 0 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: none;
                }

                .agree-button {
                    background: #4F6EF7;
                    border: none;
                    color: white;

                    &:hover {
                        background: #4461DE;
                    }

                    &:focus {
                        background: #4F6EF7;
                    }
                }

                .deny-button {
                    background: transparent;
                    border: 1px solid #E54D4D;
                    color: #E54D4D;

                    &:hover {
                        background: rgba(229, 77, 77, 0.1);
                    }

                    &:focus {
                        border-color: #E54D4D;
                        color: #E54D4D;
                    }
                }
            }
        }
    }

    .ant-modal-mask {
        background-color: rgba(0, 0, 0, 0.85) !important;
    }
}
