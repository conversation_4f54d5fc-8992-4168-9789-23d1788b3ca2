import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Popover, message } from "antd";
import { isMobileBrowser } from "@livekit/components-core";
import { ReactComponent as InfoIcon } from "../assets/icons/InfoIcon.svg";
import { ReactComponent as CopyIcon } from "../assets/icons/Copy.svg";
import { encoder } from "../utils/helper";
import "../styles/InfoPopover.scss";
import "../styles/index.scss";

export function InfoPopover({
  id,
  meetingDetails,
  sipData,
  meetingFeatures,
  websiteBaseUrl,
  inviteurl,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  const [showPopover, setShowPopover] = useState(false);
  const [showDialingNo, setShowDialingNo] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  const showMessage = (type, content) => {
    messageApi.open({
      type,
      content,
    });
  };

  function formatDateTime(dateTimeString) {
    const dateObj = new Date(dateTimeString);

    const formatTime = () => {
      let hours = dateObj.getHours();
      const minutes = dateObj.getMinutes();
      const ampm = hours >= 12 ? "PM" : "AM";
      hours %= 12;
      hours = hours || 12; // the hour '0' should be '12'
      const minutesStr = minutes < 10 ? `0${minutes}` : minutes;
      return `${hours}:${minutesStr} ${ampm}`;
    };

    const formatDate = () => {
      const day = dateObj.getDate();
      const month = dateObj.getMonth() + 1; // Months are zero-based
      const year = dateObj.getFullYear();
      const dayStr = day < 10 ? `0${day}` : day;
      const monthStr = month < 10 ? `0${month}` : month;
      return `${dayStr}/${monthStr}/${year}`;
    };

    return {
      time: formatTime(),
      date: formatDate(),
    };
  }

  const copyMeetingLink = async () => {
    // const meetingLink = `${process.env.REACT_APP_WEBSITE_BASE_URL}/v1/invitee/${encoder(
    //   id
    // )}`; // Replace with your actual meeting link
    const meetingLink = `${inviteurl}`; // Replace with your actual meeting link
    try {
      await navigator.clipboard.writeText(meetingLink);
    } catch (err) {
      // console.error("Error copying meeting link to clipboard: ", err);
      setToastNotification("Error copying meeting link to clipboard");
      setToastStatus("error");
      setShowToast(true);
    }
  };

  const copyEventDetails = async () => {
    // const meetingLink = `${process.env.REACT_APP_WEBSITE_BASE_URL}/v1/invitee/${encoder(
    //   id
    // )}`;
    const meetingLink = `${inviteurl}`; ;
    const dateAndTime = formatDateTime(meetingDetails.start_date);
    const formattedText = `Event name: ${
      meetingDetails.event_name
    }\n\nEvent Date: ${dateAndTime.date}\n\nEvent Time: ${
      dateAndTime.time
    }\n\nEvent TimeZone: ${
      meetingDetails.time_zone
    }\n\nJoining Link: ${meetingLink}\n\nPhone: ${
      sipData?.numbers
        .map((number) => {
          return `${number?.formatted_phone.split(" (")[0]} (${
            number?.formatted_phone.split(" (")[1]
          })`;
        })
        .join("\n\t") || ""
    } \n\nJoin by video syatem: ${sipData?.sip_dialing_ip} \n\nPIN: ${
      sipData?.sip_pin
    }`;

    try {
      await navigator.clipboard.writeText(formattedText);
      setToastNotification("Meeting details copied to clipboard");
      setToastStatus("success");
      setShowToast(true);
    } catch (err) {
      setToastNotification("Error copying meeting details to clipboard");
      setToastStatus("error");
      setShowToast(true);
    }
  }

  const content = (
    <div className="inf-parent-container primary-font">
      <div className="ind-title">
        {meetingDetails?.host}&apos;s {meetingFeatures?.branding_enabled === 1 
        && meetingFeatures?.branding_enabled 
        ? meetingFeatures?.branding_app_title : "Daakia"} Meeting
      </div>
      {showDialingNo && (
        <Modal
          open={showDialingNo}
          onCancel={() => {
            setShowDialingNo(false);
            setShowPopover(false);
          }}
          footer={null}
          width={400}
          className="show-dialing-modal"
        >
          <div className="show-dialing">
            <h5>More ways to join:</h5>
            {/* <div>
              <span className="show-dialing-heading">Join from the meeting link</span>
              <div className="show-dialing-link">
                <a href={`${process.env.REACT_APP_WEBSITE_BASE_URL}/v1/invitee/${encoder(id)}`}>
                  {process.env.REACT_APP_WEBSITE_BASE_URL}/v1/invitee/{encoder(id)}
                </a>
              </div>
            </div> */}
            <div className="show-dialing-phone">
              <span className="show-dialing-heading show-dialing-heading-phone">
                Join by phone
              </span>
              {sipData?.numbers.map((number, index) => (
                <div className="show-dialing-phone-number">
                  <ul>
                    <li>
                      {isMobileBrowser() ? (
                        <a
                          href={`tel:${number?.formatted_phone.split(" (")[0]}`}
                        >
                          {number?.formatted_phone.split(" (")[0]}
                        </a>
                      ) : (
                        <p key={index}>
                          {number?.formatted_phone.split(" (")[0]}{" "}
                          <span>({number?.formatted_phone.split(" (")[1]}</span>
                        </p>
                      )}
                    </li>
                  </ul>
                </div>
              ))}
            </div>
            <div className="show-dialing-ip">
              <span className="show-dialing-heading show-dialing-heading-video">
                Join by video system
              </span>
              <p>
                <span>Dial </span>&nbsp;&nbsp;{sipData?.sip_dialing_ip}
              </p>
            </div>
            <hr />
            <div className="show-dialing-phone-pin">
              {/* <p>{sipData?.sip_pin} (Enter the PIN after dialing in)</p> */}
              <p>{`Enter the PIN ${sipData?.sip_pin}, followed by # after dialing in.`}</p>
            </div>
            {/* <div className="show-dialing-numbers">
              <a href="">Global call-in numbers</a>
              <span>|</span>
              <a href="">Toll free calling restrictions</a>
            </div> */}
          </div>
        </Modal>
      )}
      <div className="inf-description">
        <div>
          <span>Host</span>
          <p>{meetingDetails?.host}</p>
        </div>
        <hr />
        <div>
          <div>
            <span>Meeting Name</span>
            <p>{meetingDetails.event_name}</p>
          </div>
          {/* {
            meetingDetails?.is_password === "1" && (
              <div>
            <span>Password</span>
            <p>SejZew</p>
          </div>
          } */}
        </div>
        <hr />
        <div>
          <span>Invitation Link</span>
          {/* <a href={`${inviteurl}`}> */}
            {/* <span>
              {process.env.REACT_APP_WEBSITE_BASE_URL}
            </span> */}
            <span style={{color:"#0077df", wordBreak:"break-all",whiteSpace:"normal",display:"block"}}>{inviteurl}</span>
          {/* </a> */}
        </div>
      </div>
      <div className="inf-meeting-links">
        <Button onClick={() => copyMeetingLink()} type="round">
          <CopyIcon />
          <span>Copy Link</span>
        </Button>
        <Button onClick={() => copyEventDetails()} type="round">
          <CopyIcon />
          <span>Copy Invite</span>
        </Button>
        {meetingFeatures?.international_phone === 1 && (
          <Button
            onClick={() => {
              setShowDialingNo(!showDialingNo);
            }}
            type="round"
          >
            <span>SIP Dialing</span>
          </Button>
        )}
      </div>
    </div>
  );
  return (
    <div
      /* onClick={() => setShowPopover(!showPopover)} */ className="inf-button"
    >
      {contextHolder}
      <Popover
        content={content}
        title={null}
        // trigger="click"
        // open={showPopover}
        onOpenChange={() => setShowPopover(!showPopover)}
        overlayInnerStyle={{
          backgroundColor: "#1e1e1e",
          width: "430px",
        }}
        open={showPopover && !showDialingNo}
        overlayClassName="inf-popover"
        // onClick={() => setShowPopover(!showPopover)}
        // onClose={() => setShowPopover(!showPopover)}
      >
        <InfoIcon />
      </Popover>
    </div>
  );
}
