import React from "react";
import { But<PERSON>, Modal } from "antd";

export function ExitWhiteboardModal({
  isExitWhiteboardModalOpen,
  setIsExitWhiteboardModalOpen,
  closeWindow,
  downloadFileToLocal
}) {
  const handleCancel = () => {
    setIsExitWhiteboardModalOpen(false);
  };

  const saveFile = async ()=>{
    await downloadFileToLocal()
    await closeWindow()
  }

  return (
    <Modal
      open={isExitWhiteboardModalOpen}
      onCancel={handleCancel}
      footer={null} // Add this if you don't want footer buttons
      className="whiteboard-exit-modal"
    >
      <div className="whiteboard-exit-modal-body">
        <div className="whiteboard-exit-modal-body-text">
          <p>You have unsaved whiteboard changes that will be lost if you decide to move away.</p>
          <p>Do you want to save the changes?</p>
        </div>
        <div className="whiteboard-exit-modal-body-buttons">
          <Button type="primary" onClick={()=>saveFile()}>Yes, Save</Button>
          <Button onClick={() => closeWindow()}>Leave</Button>
        </div>
      </div>
    </Modal>
  );
}
