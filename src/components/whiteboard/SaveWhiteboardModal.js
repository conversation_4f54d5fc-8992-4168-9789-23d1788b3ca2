import React from "react";
import { Modal } from "antd";

export function SaveWhiteboardModal({
    isSaveWhiteboardModal,
    setIsSaveWhiteboardModal,
}){
    const handleCancel = () => {
        setIsSaveWhiteboardModal(false);
    };

    return (
        <Modal
            open={isSaveWhiteboardModal}
            onCancel={handleCancel}
            footer={null} // Add this if you don't want footer buttons
        >
            <button>
                Save to local
            </button>
            <button>
                Save to cloud
            </button>
        </Modal>
    );
};
