/* eslint-disable */
import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Excalidraw,
  MainMenu,
  WelcomeScreen,
  getSceneVersion,
  serializeAsJSON,
  MIME_TYPES,
} from "@excalidraw/excalidraw";
import { datadogLogs } from "@datadog/browser-logs";
import { fileSave } from "browser-fs-access";
import { CloseOutlined } from "@ant-design/icons";
import { IoShieldCheckmarkSharp, IoCloudUploadOutline } from "react-icons/io5";
import { Tooltip } from "antd";
import { WhiteboardService } from "../../services/WhiteboardServices";
import { DataReceivedEvent, SocketChannel } from "../../utils/constants";
// import {SaveWhiteboardModal} from "./SaveWhiteboardModal"
import { ExitWhiteboardModal } from "./ExitModal";
import "./styles.scss";

export default function Whiteboard({
  setIsWhiteboardOpen,
  // isWhiteboardOpen,
  // token,
  isHost,
  allowLiveCollabWhiteBoard,
  isCoHost,
  room,
  whiteboardData,
  setWhiteboardData,
  socket,
  isSocketConnected,
  isExitWhiteboardModalOpen,
  setIsExitWhiteboardModalOpen,
  whiteboardSceneData,
  setWhiteboardSceneData,
  meetingId,
  whiteBoardId,
  meetingDetails,
  setToastNotification,
  setToastStatus,
  setShowToast,
  coHostToken,
  apiToken,
  meetingFeatures,
  devMode,
}) {
  const excalidrawRef = useRef(null);
  const excalidrawWrapperRef = useRef(null);
  const [dimensions, setDimensions] = useState({
    width: undefined,
    height: undefined,
  });
  const [excaliDrawAPI, setExcaliDrawAPI] = useState(null);

  const [maxUpdatedAt, setMaxUpdatedAt] = useState(0);

  // Main function to handle scene version updates
  // const sendWhiteboardData = (elements) => {
  //   const maxUpdated = elements.reduce((max, obj) => (obj.updated > max ? obj.updated : max), -Infinity);
  //   if (!isSocketConnected || !socket) return; // Ensure socket is connected
  //   // if (currentSceneVersion > prevSceneVersion) {
  //     if (maxUpdated > maxUpdatedAt) {
  //       setMaxUpdatedAt(maxUpdated);
  //       socket.emit(SocketChannel.WHITEBOARD_UPDATE, { elements });
  //     }
  // };

  useEffect(() => {
    const startingSceneVersion = getSceneVersion(whiteboardData?.elements);
    setWhiteboardSceneData((prevData) => ({
      ...prevData,
      start: startingSceneVersion,
      end: startingSceneVersion,
    }));
  }, []);

  // useEffect(()=>{
  //   console.log(whiteboardSceneData)
  // },[whiteboardSceneData])
  const downloadFileToLocal = async () => {
    try {
      const file = excaliDrawAPI.getFiles();
      const serialize = serializeAsJSON(
        whiteboardData?.elements,
        whiteboardData?.appState,
        file,
        "local"
      );
      const blob = new Blob([serialize], {
        type: MIME_TYPES.excalidraw,
      });
      await fileSave(blob, {
        fileName: meetingDetails?.event_name || `Drawing${Date.now()}`,
        extensions: [".excalidraw"],
        description: "Daakia Whiteboard Drawing",
      });
    } catch (error) {
      datadogLogs.logger.error("Error while saving whiteboard data in local", {
        error,
      });
    }
  };

  const closeWhiteboard = async () => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.WHITEBOARD_STATE,
        value: false,
        whiteboardId: whiteBoardId,
      })
    );
    try {
      const response = await WhiteboardService.updateWhiteboardStatus(
        whiteBoardId,
        false,
        meetingId,
        isHost ? apiToken : coHostToken,
        room?.localParticipant?.participantInfo,
        devMode
      );
      if (response.success === 0) {
        console.log("Error in updating whiteboard status");
      }
    } catch (error) {
      console.log(error);
    }
    room.localParticipant.publishData(data, {
      reliable: true,
    });
    setWhiteboardSceneData((prevData) => ({
      ...prevData,
      start: prevData.end,
    }));
    setIsWhiteboardOpen(false);
    setIsExitWhiteboardModalOpen(false);
  };

  useEffect(() => {
    const observer = new MutationObserver((mutationsList) => {
      for (let mutation of mutationsList) {
        if (mutation.type === "childList") {
          const element = document.querySelector(".dialog-mermaid-desc");
          if (element) {
            element.innerHTML =
              "Right now only flowcharts and sequence diagrams are supported. More would be available soon.";
            observer.disconnect(); // Stop observing once the element is found and updated
          }
        }
      }
    });

    // Start observing the body for added elements
    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect(); // Cleanup on component unmount
  }, []);

  const sendWhiteboardData = useCallback(
    (elements) => {
      const maxUpdated = elements.reduce(
        (max, obj) => (obj.updated > max ? obj.updated : max),
        -Infinity
      );

      if (!isSocketConnected || !socket) return; // Ensure socket is connected

      if (maxUpdated > maxUpdatedAt) {
        setMaxUpdatedAt(maxUpdated);
        setWhiteboardData({ elements, appState: excaliDrawAPI.getAppState() });
        const currentSceneVersion = getSceneVersion(elements);
        setWhiteboardSceneData((prevData) => ({
          ...prevData,
          end: currentSceneVersion,
        }));
        socket.emit(SocketChannel.WHITEBOARD_UPDATE, { elements });
      }
    },
    [isSocketConnected, socket, maxUpdatedAt]
  );

  // Handle receiving updates from the server
  useEffect(() => {
    if (!isSocketConnected || !socket) return;
    const updateScene = (data) => {
      const currentElements = excaliDrawAPI.getSceneElementsIncludingDeleted();
      const maxCurrentUpdated = currentElements.reduce(
        (max, obj) => (obj.updated > max ? obj.updated : max),
        -Infinity
      );
      const maxDataUpdated = data.elements.reduce(
        (max, obj) => (obj.updated > max ? obj.updated : max),
        -Infinity
      );
      if (maxDataUpdated > maxCurrentUpdated) {
        const sceneData = {
          elements: data.elements,
          appState: excaliDrawAPI.getAppState(),
        };
        if (!Array.isArray(sceneData.appState.collaborators)) {
          sceneData.appState.collaborators = [];
        }
        // excalidrawRef.current.updateScene(sceneData);
        const currentSceneVersion = getSceneVersion(sceneData?.elements);
        excaliDrawAPI.updateScene(sceneData);
        setWhiteboardData(sceneData);
        setWhiteboardSceneData((prevData) => ({
          ...prevData,
          end: currentSceneVersion,
        }));
      }
    };
    socket.on(SocketChannel.WHITEBOARD_UPDATE, updateScene);
    // socket.on(SocketChannel.WHITEBOARD_DATA_REQUEST,updateScene)

    return () => {
      socket.off(SocketChannel.WHITEBOARD_UPDATE, updateScene); // Clean up listener
      // socket.off(SocketChannel.WHITEBOARD_DATA_REQUEST,updateScene)
    };
  }, [isSocketConnected, socket]);

  useEffect(() => {
    setDimensions({
      width: excalidrawWrapperRef.current.getBoundingClientRect().width,
      height: excalidrawWrapperRef.current.getBoundingClientRect().height,
    });
    const onResize = () => {
      setDimensions({
        width: excalidrawWrapperRef.current.getBoundingClientRect().width,
        height: excalidrawWrapperRef.current.getBoundingClientRect().height,
      });
    };

    window.addEventListener("resize", onResize);

    return () => window.removeEventListener("resize", onResize);
  }, [excalidrawWrapperRef]);

  return (
    <div className="excalidraw-wrapper" ref={excalidrawWrapperRef}>
      {(isHost || isCoHost) && (
        <CloseOutlined
          onClick={() =>
            whiteboardSceneData?.start !== whiteboardSceneData?.end
              ? setIsExitWhiteboardModalOpen(true)
              : closeWhiteboard()
          }
          className={`whiteboard-close`}
        />
      )}

      <Excalidraw
        ref={excalidrawRef}
        width={dimensions.width}
        height={dimensions.height}
        initialData={whiteboardData}
        onChange={(elements) => {
          sendWhiteboardData(elements);
        }}
        excalidrawAPI={(api) => setExcaliDrawAPI(api)}
        // onPointerUpdate={(payload) => console.log("payload:", payload)}
        // onCollabButtonClick={() => window.alert("You clicked on collab button")}
        viewModeEnabled={!isHost && !isCoHost && !allowLiveCollabWhiteBoard}
        // zenModeEnabled={zenModeEnabled}
        // gridModeEnabled={gridModeEnabled}
      >
        {(isHost || isCoHost || allowLiveCollabWhiteBoard) && <WelcomeScreen />}
        <MainMenu>
          <MainMenu.DefaultItems.LoadScene />
          <MainMenu.DefaultItems.Export />
          {/* <MainMenu.Item
            onSelect={() => downloadFileToLocal()}
            icon={<IoCloudUploadOutline />}
          >
            Save to Cloud
          </MainMenu.Item> */}
          <MainMenu.DefaultItems.SaveAsImage />
          <MainMenu.DefaultItems.ClearCanvas />
          <MainMenu.DefaultItems.ToggleTheme />
          <MainMenu.DefaultItems.ChangeCanvasBackground />
        </MainMenu>
      </Excalidraw>
      <Tooltip
        title={
          `Your drawings are end-to-end encrypted so ${
            meetingFeatures?.branding_enabled === 1 &&
              meetingFeatures?.branding_app_title
                ? meetingFeatures?.branding_app_title
                : "Daakia"
          }'s servers will never see them.`
        }
        placement="topRight"
      >
        <IoShieldCheckmarkSharp
          className={`whiteboard-encryption ${
            (isHost || isCoHost) && "whiteboard-encryption-host"
          }`}
        />
      </Tooltip>
      <ExitWhiteboardModal
        isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
        setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
        closeWindow={closeWhiteboard}
        downloadFileToLocal={downloadFileToLocal}
      />
      {/* <SaveWhiteboardModal isSaveWhiteboardModal={isSaveWhiteboardModal} setIsSaveWhiteboardModal={setIsSaveWhiteboardModal}/> */}
    </div>
  );
}
