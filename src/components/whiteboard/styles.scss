.excalidraw-wrapper {
  height: 100%;
  width: 100%;
  margin-left: 0.5rem;
  position: relative;
  .excalidraw-container {
    border-radius: 6px;
  }
  .whiteboard-close {
    position: absolute;
    color: rgb(122, 122, 122);
    z-index: 100;
    right: 1.5rem;
    top: 1.5rem;
    border: 1px solid rgb(122, 122, 122);
    padding: 0.2rem;
    @media screen and (max-width: 760px){
      right: 0.5rem;
      top: 0.5rem;
    }
    @media screen and (max-width: 700px){
      right: 0.1rem;
      top: 1.5rem;
    }
    @media screen and (max-width: 450px){
      // right: 0.5rem;
      top: 3rem;
      z-index: 10;
    }
  }
  .whiteboard-encryption{
    position: absolute;
    color: rgb(62, 65, 155);
    z-index: 10;
    right: 4.5rem;
    bottom: 1.3rem;
    font-size: 2rem;
    &-host{
      @media screen and (max-width: 450px){
        left: 1rem;
        top: 4rem !important;
      }
    }
    @media screen and (max-width: 917px){
      right: 1rem;
      bottom: 4.5rem;      
    }
    @media screen and (max-width: 450px){
      top: 1rem;
    }
    &-popover{
      .ant-popover{
        &-inner{
          background-color: #000 !important;

        }
        &-inner-content{
        }
      }
    }
  }
}

.excalidraw {
  .dropdown-menu {
    display: unset !important;
    padding: unset !important;
  }

  .sidebar-trigger {
    display: none !important;
  }
  .HelpDialog__header {
    display: none !important;
  }
  .welcome-screen-center__logo {
    display: none !important;
  }
}

.excalidraw .FixedSideContainer > *{
  @media screen and (max-width: 450px){
    margin-top: -0.9rem;
    // margin-right: 1.5rem;
  }
}
.mobile-misc-tools-container{
  @media screen and (max-width: 450px){
    margin-top: 2rem;
  }    
}
.whiteboard-exit-modal{
  .ant-modal-content{
    border-radius: 10px;
  }
  &-body{
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-text{
      display: flex;
      flex-direction: column;
      p{
        &:nth-child(1){
          font-size: 18px;
          margin-top: 1rem;
        }
        &:nth-child(2){
          font-size: 18px;
        }
      }
    }
    &-buttons{
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      .ant-btn {
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        span{
          font-size: 16px;
        }
      }
    }
  }
}

.whiteboard-focus{
  padding: 0.5rem;
  padding-bottom: 0.2rem;
  @media screen and (max-width: 450px){
    flex-direction: column;
    gap: 0.5rem;
    .lk-carousel{
      height: auto;
      .ant-avatar{
        height: 50px;
        width: 50px;
        font-size: 1rem;
      }
    }
    .excalidraw-wrapper{
      margin: 0;
    }
  }
}