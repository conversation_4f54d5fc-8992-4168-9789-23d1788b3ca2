import React from "react";

import "../../styles/ControlBar.scss";
import "../../styles/Settings.scss";
import "../../styles/index.scss";
import { ReactComponent as SvgRaiseHandIcon } from "./icons/RaiseHandIcon.svg";
import { ReactComponent as SvgRaiseHandOnIcon } from "./icons/RaiseHandON.svg";

export function RaiseHandControlButton({ showRaiseHand, setShowRaiseHand }) {
  return (
    <div
      onClick={() => setShowRaiseHand(!showRaiseHand)}
      className="lk-button control-bar-button control-bar-button-icon"
    >
      {showRaiseHand ? <SvgRaiseHandOnIcon /> : <SvgRaiseHandIcon />}
    </div>
  );
}
