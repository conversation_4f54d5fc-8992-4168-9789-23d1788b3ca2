.raise-hand-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px;
  pointer-events: none; /* Allows clicks to pass through to the underlying component */
  transition: transform 0.3s ease-in-out; /* Smooth transition for bounce */
  .hand-raised {
    font-size: 24px;
    margin-bottom: 5px;
    svg {
      width: 48px;
      height: 48px;
    }
  }
}
.bounce-animation {
  animation: bounce 0.5s infinite alternate; /* Bounce animation */
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px); /* Adjust bounce height as needed */
  }
}
.reactions {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.reaction {
  font-size: 20px;
  margin: 2px 0;
}
