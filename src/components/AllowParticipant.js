import { <PERSON><PERSON>, Avatar } from "antd";
import React, { useCallback } from "react";
import { ParticipantService } from "../services/ParticipantServices";
import { generateAvatar } from "../utils/helper";

export default function AllowParticipant({
  participantName,
  setLobbyParticipants,
  lobbyParticipants,
  requestId,
  id,
  setShowToast,
  coHostToken,
  localParticipant,
  apiToken,
  isCoHost,
  devMode
}) {
  const avatarName = generateAvatar(participantName);

  const handleStatus = useCallback(
    async (status) => {
      setShowToast(false);
      try {
        const response = await ParticipantService.lobbyParticipantStatusUpdate(
          id,
          requestId,
          status,
          isCoHost ? coHostToken : apiToken,
          localParticipant?.participantInfo,
          devMode
        );

        if (response.success === 0) {
          console.log("Error updating participant status", response);
        }

        setLobbyParticipants((prev) => {
          const updatedMap = new Map(prev);
          updatedMap.delete(requestId); // Remove participant from map
          return updatedMap;
        });
      } catch (error) {
        console.error("Error updating participant status", error);
      }
    },
    [lobbyParticipants, setLobbyParticipants]
  );

  return (
    <div className="pwj-toast">
      <p>New participant wants to join!</p>
      <div className="pwj-name-card">
        <Avatar style={{ backgroundColor: "#fd4563", verticalAlign: "middle" }}>
          {avatarName}
        </Avatar>
        <span>{participantName}</span>
      </div>
      <div>
        <Button type="primary" onClick={() => handleStatus(true)}>
          Allow
        </Button>
        <Button type="text" onClick={() => handleStatus(false)} style={{ color: "#fff" }}>
          Deny
        </Button>
      </div>
    </div>
  );
}
