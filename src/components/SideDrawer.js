import React from "react";
import { IoMdClose } from "react-icons/io";
import { isMobileBrowser } from "@livekit/components-core";
import { ActionPopover } from "./participants/ActionPopover";
import "../styles/SideDrawer.scss";
import { DrawerState } from "../utils/constants";

export default function SideDrawer({
  className,
  show,
  setShow,
  title,
  children,
  isHost,
  isCoHost,
  localParticipant,
  setRemoteRaisedHands,
  setShowRaiseHand,
  count,
  meetingFeatures,
  isWhiteboardOpen,
  isLiveCaptionsDrawerOpen,
  setDrawerState,
}) {
  return (
    <div
      className={`sd-container 
        ${show ? "show" : "hide"} 
        ${className} 
        ${isMobileBrowser() ? "show-chat-mobile" : ""}
        ${isWhiteboardOpen ? "sd-container-whiteboard-open" : ""}`}
    >
      <div className="sd-container-inner">
        <div className="sd-header">
          <div className="sd-title primary-font">
            {" "}
            {title} {title === "Participants" && `(${count})`}
            {(isHost || isCoHost) &&
              title === "Participants" &&
              (meetingFeatures?.raise_hand === 1 ||
                // meetingFeatures?.disable_camera === 1 ||
                meetingFeatures?.mute_participant === 1) && (
                <ActionPopover
                  localParticipant={localParticipant}
                  setRemoteRaisedHands={setRemoteRaisedHands}
                  setShowRaiseHand={setShowRaiseHand}
                  meetingFeatures={meetingFeatures}
                />
              )}
          </div>
          <div
            style={{ color: "white" }}
            className="lk-button sd-button"
            onClick={() => {
              setShow(false);
              setDrawerState(DrawerState.NONE);
            }}
          >
            <IoMdClose />
          </div>
        </div>
        <div 
          className={
            `sd-container-below
            ${isLiveCaptionsDrawerOpen ? "sd-container-below-live-captions" : ""}`
          }>{children}</div>
      </div>
    </div>
  );
}
