import React, { useState } from "react";
import { IoMdCheckmark, IoMdInformation } from "react-icons/io";
import { VscClose } from "react-icons/vsc";
import { BsExclamationLg } from "react-icons/bs";
import "./statusNotification.scss";

export default function StatusNotification({ status, message }) {
  const [visible, setVisible] = useState(true);
  const notifications = {
    success: {
      // type: "Success",
      icon: <IoMdCheckmark className="check" />,
      className: "statusNotification-success",
    },
    error: {
      // type: "Error",
      icon: <VscClose className="Close" />,
      className: "statusNotification-error",
    },
    warning: {
      // type: "Warning",
      icon: <BsExclamationLg className="exclamation" />,
      className: "statusNotification-warning",
    },
    info: {
      // type: "Info",
      icon: <IoMdInformation className="information" />,
      className: "statusNotification-info",
    },
    content: {
      type: "Content",
      icon: null,
      className: "statusNotification-content",
    },
  };

  const notification = notifications[status];
  if (!notification || !visible) return null;

  return (
    <div className={`statusNotification statusNotification-${status}-bg`}>
        <div className="statusNotification-box">
          {status === "content" ? null : (
            <div className="statusNotification-box-circle-outer">
              <div
                className={`statusNotification-box-circle-inner statusNotification-${status}`}
              >
                {notification.icon && notification.icon}
              </div>
            </div>
          )}
          <div className="statusNotification-text">
            {/* {status === "content" ? null : <p>{notifications.type}</p>} */}
            <span>{message}</span>
          </div>
          <button
            className="statusNotification-close"
            onClick={() => setVisible(false)}
          >
            <VscClose />
          </button>
        </div>
    </div>
  );
}
