import React from "react";
import { ReactComponent as LoadingIcon } from "../assets/icons/Loading.svg";
import "../styles/Prejoin.scss";

export function Loader({ heading, description, isLoading }) {
  return (
    <div>
      <div className="loading-parent-container">
        <div className="loading-title">{heading}</div>
        <div className="loading-description">{description}</div>
        {isLoading === true ? <LoadingIcon className="rotate" /> : null}
      </div>
    </div>
  );
}
