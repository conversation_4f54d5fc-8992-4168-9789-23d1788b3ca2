@import "../../styles/variables";

.ss-parent {
  width: 11vw;
  @media (max-width: 950px) {
    width: 12vw;
  }
  .ss-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4% 6%;
    cursor: pointer;
    width: 100%;
    border-radius: 7px;
    color: white;
    font-size: 0.8rem;
    font-weight: 700;
    margin-bottom: 10px;
    background-color: rgb(72, 72, 72);
    font-family: $font;
    &:hover {
      background-color: rgb(95, 93, 92) !important;
    }
    @media (max-width: 950px) {
      font-size: 0.7rem; /* Reduce font size */
      padding: 3% 5%; /* Adjust padding as needed */
      border-radius: 5px;
    }
  }
}
.screen-share-modal {
  width: 35rem !important;
  .ant-modal-header {
    border-radius: 6px 6px 0 0;
    .ant-modal-title {
      font-size: 1.2rem;
      font-weight: 600;
    }
  }
  .ant-modal-content {
    border-radius: 6px;
    .ant-modal-body{
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
  }
  .screen-share{
    &-audio{
      display: flex;
      align-items: center;
      gap: 1rem;
      span{
        font-size: 1.1rem;
        font-weight: 600;
      }
      .ant-switch-checked{
        background-color: $join-button-enabled;
      }
    }
    &-tabs {
      .ant-tabs-tabpane-active {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: flex-start;
      }
    }
  }
}
.screen-share {
  &-tabs{
    .ant-tabs-tab{
      &-btn{
        font-size: 1rem;
        font-weight: 600;
      }
    }
  }
  &-option {
    display: flex;
    flex-direction: column;
    width: 10rem;
    // height: 7.4rem;
    height: auto;
    overflow: hidden;
    img {
      padding: 0.5rem;
      border: 2px solid white;
      width: 100%;
      height: 6rem;
      object-fit: contain;
      &:hover {
        border: 2px solid rgb(62, 62, 62);
      }
    }
    p {
      margin: 0;
      margin-left: 0.5rem;
    }
  }
}
.selected-screen-share {
  display: flex;
  flex-direction: column;
  width: 10rem;
  img {
    border: 2px solid $join-button-enabled;
    padding: 0.5rem;
    &:hover {
      border: 2px solid rgb(62, 62, 62);
    }
  }
}
