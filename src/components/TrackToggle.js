import React, { forwardRef } from "react";
import { Track } from "livekit-client";
import { constants } from "../utils/constants";
import { getSourceIcon,setLocalStorage,getLocalStorage } from "../utils/helper";
import { useTrackToggle } from "../hooks/useTrackToggle";

/**
 * With the `TrackToggle` component it is possible to mute and unmute your camera and microphone.
 * The component uses an html button element under the hood so you can treat it like a button.
 *
 * @example
 * ```tsx
 * <LiveKitRoom>
 *   <TrackToggle source={Track.Source.Microphone} />
 *   <TrackToggle source={Track.Source.Camera} />
 * </LiveKitRoom>
 * ```
 * @public
 */
export const TrackToggle = forwardRef(function TrackToggle(
  { showIcon, ...props },
  ref
) {
  const { buttonProps, enabled } = useTrackToggle(props);
  const { source } = props;
  let userChoice = getLocalStorage(constants.MEETING_USER_CHOICES);

  if(!userChoice || Object.keys(userChoice).length === 0){
    userChoice = {video: false, audio: false};
  }
  if(source === Track.Source.Camera && userChoice.video !== enabled){
    userChoice.video = enabled;
  }
  if(source === Track.Source.Microphone && userChoice.audio !== enabled){
    userChoice.audio = enabled;
  }
  
  setLocalStorage(constants.MEETING_USER_CHOICES, userChoice);
  return (
    <button ref={ref} {...buttonProps}>
      {(showIcon ?? true) && getSourceIcon(props.source, enabled)}
      {props.children}
    </button>
  );
});

