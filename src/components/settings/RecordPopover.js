import React, { useState } from "react";

import { Popover } from "antd";
import { Endpoints } from "../../API/Endpoints/routes";
import { APIrequest } from "../../API/axios";

import { ReactComponent as RecordingIcon } from "../../assets/icons/Recording.svg";

import "../../styles/Settings.scss";
import "../../styles/index.scss";

export function RecordPopover({
  id,
  setIsRecording,
  setEgressId,
  setIsCloudRecording,
  setMoreOptionShowPopover,
}) {
  const [showPopover, setShowPopover] = useState(false);

  const startCloudRecording = async () => {
    setShowPopover(false);
    try {
      const response = await APIrequest({
        method: Endpoints.recording_start.method,
        endpoint: Endpoints.recording_start.url,
        payload: {
          meeting_uid: id,
        },
      });

      if (response.success === 0) {
        console.log("Error in starting recording", response.message);
        return;
      }
      setEgressId(response.data.egress_id);
      setIsRecording(true);
      setIsCloudRecording(true);
    } catch (error) {
      console.log("Error in recording", error);
    }
  };

  const startLocalRecording = async () => {
    console.log("Local recording started");
    setIsRecording(true);
    setIsCloudRecording(false);
    setShowPopover(false);
  };

  const content = (
    <div
      style={{
        width: "8vw",
        display: "flex",
        flexDirection: "column",
        gap: "0.5rem",
      }}
    >
      <div
        className="settings-menu-item record-items"
        onClick={() => {
          startLocalRecording();
          setMoreOptionShowPopover(false);
        }}
      >
        <div style={{ width: "100%" }} className="settings-menu-inner-text">
          Local
        </div>
      </div>
      <div
        className="settings-menu-item record-items"
        onClick={() => {
          startCloudRecording();
          setMoreOptionShowPopover(false);
        }}
      >
        <div style={{ width: "100%" }} className="settings-menu-inner-text">
          Cloud
        </div>
      </div>
    </div>
  );
  return (
    <Popover
      content={content}
      trigger="click"
      title={null}
      placement="left"
      onOpenChange={() => setShowPopover(!showPopover)}
      open={showPopover}
      overlayInnerStyle={{
        backgroundColor: "#000",
        borderRadius: "6px",
        paddingTop: "0px",
      }}
      overlayClassName="record-popover"
    >
      <div
        className="settings-menu-item primary-font"
        // onClick={() => (isRecording ? stopRecording(id) : startRecording(id))}
        // onClick={() => setShowPopover(!showPopover)}
      >
        <div className="settings-menu-inner-icon">
          <RecordingIcon />
        </div>
        <div className="settings-menu-inner-text">Record</div>
      </div>
    </Popover>
  );
}
