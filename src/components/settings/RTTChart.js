import React, { useState, useEffect } from "react";
import ApexCharts from "apexcharts";
import Chart from "react-apexcharts";
// import { useTroubleShootingStatsContext } from "../../contexts/performanceoptions/TroubleShootingStatsContext";

export default function RTTChart({ title, caption, roundTripTimes }) {
  //   const performanceOptions = useTroubleShootingStatsContext();
  
  // const performanceOptions = {
  //   troubleShootingStats: {
  //     rtt: [
  //       { x: new Date().getTime() - 60000, y: 100 },
  //       { x: new Date().getTime() - 30000, y: 200 },
  //       { x: new Date().getTime(), y: 150 },
  //     ],
  //   },
  // };
  const [rttChartState] = useState(() => {
    return {
      options: {
        title: {
          text: title,
        },
        subtitle: {
          text: caption,
        },
        tooltip: {
          x: {
            format: "hh:mm:ss TT",
          },
        },
        stroke: {
          width: 2,
        },
        chart: {
          id: "rtt-chart",
          toolbar: {
            show: false,
          },
        },
        yaxis: {
          tickAmount: 2,
          min: 0,
          max: 600,
          title: {
            text: "milliseconds",
          },
          opposite: true,
        },
        xaxis: {
          type: "datetime",
          labels: {
            datetimeUTC: false,
            showDuplicates: false,
            format: "hh:mm TT",
          },
          title: {
            text: "time since now",
          },
        },
      },
      series: [
        {
          name: "server response time (ms)",
          data: roundTripTimes,
        },
      ],
    };
  });

  useEffect(() => {
    ApexCharts.exec("rtt-chart", "updateSeries", [
      {
        data: roundTripTimes,
      },
    ]);
  }, [roundTripTimes]);

  return (
    <Chart
      options={rttChartState.options}
      series={rttChartState.series}
      type="line"
      height={200}
      width={585}
    />
  );
}
