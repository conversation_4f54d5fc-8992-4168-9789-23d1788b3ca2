import { Modal } from "antd";
import React, { useEffect, useRef, useState } from "react";
import RTT<PERSON><PERSON> from "./RTTChart";
import { useTroubleShoot } from "../../hooks/useTroubleShoot";
import { encoder } from "../../utils/helper";
import {ReactComponent as NetworkIcon} from "../../assets/icons/Network.svg";
// import SystemPerformanceIco from "../../assets/icons/SystemPerformance.svg";
import "../../styles/TroubleShootAndHelp.scss";
import "../../styles/index.scss";

export function TroubleShootAndHelp({
  isTroubleshooting,
  setIsTroubleshooting,
  room,
  id,
}) {
  const [networkStatus, setNetworkStatus] = useState(1);
  const encodedId = encoder(id);
  const roundTripTimes = useTroubleShoot(room);
  const handleCancel = () => {
    setIsTroubleshooting(false);
  };

  const tshBodyContentRef = useRef(null);

  useEffect(() => {
    if (isTroubleshooting && tshBodyContentRef.current) {
      tshBodyContentRef.current.scrollTop = 0; // Set scrollTop to 0 to scroll to top
    }
  }, [isTroubleshooting]);

  useEffect(() => {
    const calculateNetworkStatus = () => {
      const now = Date.now();
      const lastMinuteRTTs = roundTripTimes.filter(([x]) => now - x <= 60000);
      const avgRTT =
        lastMinuteRTTs.reduce((sum, [, y]) => sum + y, 0) /
        lastMinuteRTTs.length;

      if (avgRTT < 150) {
        setNetworkStatus(1);
      } else if (avgRTT >= 150 && avgRTT <= 300) {
        setNetworkStatus(2);
      } else {
        setNetworkStatus(3);
      }
    };

    const intervalId = setInterval(calculateNetworkStatus, 60000); // Calculate every 60 seconds

    return () => clearInterval(intervalId); // Cleanup interval on unmount
  }, [roundTripTimes]);

  return (
    <Modal
      title={null}
      open={isTroubleshooting}
      footer={null}
      onCancel={handleCancel}
      bodyStyle={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        backgroundColor: "#1c1c1e",
        color: "white",
      }}
      rootClassName="tsh"
      className="primary-font tsh-modal"
    >
      <div className="tsh-modal-header">
        <p className="troubleshoot-meeting-header">Meeting {encodedId}</p>
        <hr />
      </div>
      <div className="tsh-body-content" ref={tshBodyContentRef}>
        <div className="tsh-network-connections">
          <div className="tsh-network-connections-nc">
            <NetworkIcon />
            <span>Network Connection</span>
            {/* Good/Average/Bad */}
            <div className="tsh-network-connections-nc-quality">
              {networkStatus === 1 && (
                <span className="tsh-network-connections-nc-quality-good">
                  Good
                </span>
              )}
              {networkStatus === 2 && (
                <span className="tsh-network-connections-nc-quality-average">
                  Moderate
                </span>
              )}
              {networkStatus === 3 && (
                <span className="tsh-network-connections-nc-quality-bad">
                  Poor
                </span>
              )}
            </div>
          </div>
          {networkStatus === 2 ||
            (networkStatus === 3 && (
              <div>
                <p>Recommendations</p>
                <ul>
                  <li>Move closer to Wi-Fi router</li>
                  <li>
                    Make sure multiple devices are not connected to same router
                  </li>
                </ul>
              </div>
            ))}
          <RTTChart
            title="DELAY IN CONNECTION"
            caption="High values or large variations may reduce call quality"
            roundTripTimes={roundTripTimes}
          />
        </div>
        <hr />
        {/* <div className="tsh-network-connections">
            <span>
              <img src={SystemPerformanceIco} alt="" />
              System Performace
            </span>
            <div>
              <p>Recommendations</p>
              <ul>
                <li>Close browser tabs that are not required</li>
                <li>Close other apps if not in use</li>
              </ul>
            </div>
            <RTTChart
              title="CPU USAGE"
              caption="Very high values may reduce call quality"
            />
          </div>
          <hr /> */}
      </div>
    </Modal>
  );
}
