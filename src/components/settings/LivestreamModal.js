import React, { useState } from "react";
import { Modal, Input, Button } from "antd";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import "../../styles/LiveStreamModal.scss";
import "../../styles/index.scss";

export function LivestreamingUrlModal({
  isLiveStreamModalOpen,
  setIsLiveStreamModalOpen,
  isLiveStreaming,
  setIsLiveStreaming,
  // handleUrlSubmit,
  id,
  apiToken,
  coHostToken,
  isHost,
  localParticipant,
  devMode
}) {
  const [egressId, setEgressId] = useState(null);
  const [streamKey, setStreamKey] = useState(""); // State for stream key
  const [url, setUrl] = useState(""); // State for URL

  const handleCancel = () => {
    setIsLiveStreamModalOpen(false);
  };

  const livestreamStart = async (meetingId) => {
    try {
      const response = await SettingsMenuServices.livestreamStart(
        meetingId,
        url,
        streamKey,
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
      if (response.success === 1) {
        setEgressId(response.data.egress_id);
        setIsLiveStreaming(true);
      }
      return response;
    } catch (error) {
      console.log("Error in livestreaming", error);
    }
  };

  const livestreamStop = async (meetingId) => {
    try {
      const response = await SettingsMenuServices.livestreamStop(
        meetingId,
        egressId,
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
      if (response.success === 1) {
        setIsLiveStreaming(false);
        setEgressId(null);
      }
      return response;
    } catch (error) {
      console.log("Error in stopping livestream", error);
    }
  };

  const handleSubmit = () => {
    // handleUrlSubmit();
    if (isLiveStreaming) {
      livestreamStop(id);
    } else {
      livestreamStart(id);
    }
    setIsLiveStreamModalOpen(false);
  };

  return (
    <Modal
      title={null}
      open={isLiveStreamModalOpen}
      footer={null}
      onCancel={handleCancel}
      bodyStyle={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        backgroundColor: "#1c1c1e",
        color: "white",
      }}
      className="ls-modal"
    >
      {isLiveStreaming ? (
        <div className="ls-container-parent  primary-font">
          <p className="ls-title">End Stream</p>
          <p className="ls-description">
            Your stream will stop immediately & you will no longer be live!
          </p>
          <div className="ls-button-container">
            <Button
              type="primary"
              size="large"
              className="ls-button ls-button-cancel"
              onClick={() => setIsLiveStreamModalOpen(false)}
              ghost
            >
              Cancel
            </Button>
            <Button
              type="danger"
              size="large"
              className="ls-button"
              onClick={() => handleSubmit()}
            >
              End
            </Button>
          </div>
        </div>
      ) : (
        <div className="ls-container-parent  primary-font">
          <p className="ls-title">Enter Live Streaming Account Details</p>
          <div className="ls-form-input-container">
            <p>
              Stream Key<span className="text-danger">*</span>
            </p>

            <Input
              size="large"
              placeholder="Enter stream key"
              onChange={(e) => setStreamKey(e.target.value)}
              className="ls-input"
            />
          </div>
          <div className="ls-form-input-container">
            <p>
              Stream Url<span className="text-danger">*</span>
            </p>
            <Input
              size="large"
              placeholder="Enter stream url"
              onChange={(e) => setUrl(e.target.value)}
              className="ls-input"
            />
          </div>
          <div className="ls-button-container">
            <Button
              type="primary"
              size="large"
              className="ls-button ls-button-cancel"
              onClick={() => setIsLiveStreamModalOpen(false)}
              ghost
            >
              Cancel
            </Button>
            <Button
              type="primary"
              size="large"
              className="ls-button"
              onClick={() => handleSubmit()}
            >
              Start
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
}
