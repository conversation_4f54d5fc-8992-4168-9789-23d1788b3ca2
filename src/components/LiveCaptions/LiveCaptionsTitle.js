// import { Popover } from "antd";
import React, { useEffect } from "react";
import { Mo<PERSON>, Popconfirm, Popover } from "antd";
// import { ReactComponent as AudioIco } from "./Assets/Audio.svg";
// import { ReactComponent as VideoIco } from "./Assets/Video.svg";
// import { ReactComponent as LiveCapIco } from "./Assets/LiveCap.svg";
import { TfiClose } from "react-icons/tfi";
import { ReactComponent as LanguageSelectionIco } from "./Assets/Language.svg";
// import { ReactComponent as LanguageSelectionIco2 } from "./Assets/ic_translate_chats_blue.svg";
import { ReactComponent as LanguageSelectionIco2 } from "./Assets/ic_translate_chats_colored.svg";
import { ReactComponent as DownloadCaptions } from "./Assets/Download.svg";
// import { ReactComponent as LiveCapSettings } from "./Assets/Settings.svg";
import LanguageSelection from "./LanguageSelection";

export default function LiveCaptionsTitle({
  isLanguageSelected,
  downloadCaptions,
  handleLanguageSelection,
  isHost,
  isCoHost,
  setTranslationDetails,
  translationDetails,
  meetingFeatures,
}) {
  const [isModalVisible, setIsModalVisible] = React.useState(false);
  // const popoverContent = (
  //   <div className="live-cap-settings-items">
  //     {(!isCoHost || !isHost) && (
  //       <div
  //         className="live-cap-settings-items-item"
  //         onClick={() => setIsModalVisible(true)}
  //       >
  //         <LanguageSelectionIco />
  //         <p>Choose Language</p>
  //       </div>
  //     )}
  //     <div className="live-cap-settings-items-item">
  //       <AudioIco />
  //       <p>Audio</p>
  //     </div>
  //     <div className="live-cap-settings-items-item">
  //       <VideoIco />
  //       <p>Video</p>
  //     </div>
  //     <div className="live-cap-settings-items-item">
  //       <LiveCapIco />
  //       <p>Live Captions</p>
  //     </div>
  //   </div>
  // );

  const popoverContent = <div>Choose Language</div>;

  const handleSelectedLanguage = (langId, language) => {
    setIsModalVisible(false);
    if (isHost || isCoHost) {
      handleLanguageSelection(langId, language);
    } else {
      setTranslationDetails({
        ...translationDetails,
        target_lang: langId,
      });
    }
    // console.log(language);
  };
  const text = "Click here to choose your transcript language.";
  const [tip, setTip] = React.useState(
    translationDetails.target_lang === ""
  );

  useEffect(() => {
    if (translationDetails.target_lang !== "") {
      setTip(false);
    }
  }, [translationDetails.target_lang]);

  // console.log("meetingFeatures", meetingFeatures);
  // console.log("tip", tip);
  // console.log("translationDetails", translationDetails);

  return (
    <div className="live-captions-title">
      {isLanguageSelected ? (
        <div className="live-captions-title-live">
          <h2>Live Captions</h2>
          {isCoHost || isHost ? (
            <div className="live-cap-settings-icon live-cap-settings-icon-host">
              {/* <Popover
                content={popoverContent}
                overlayClassName="choose-live-cap-host"
                placement="bottomRight"
                align={{ offset: [15, 0] }}
                // trigger={["click"]}
              >
                <LanguageSelectionIco2 
                  onClick={() => setIsModalVisible(true)}
                />
              </Popover> */}
              <DownloadCaptions onClick={downloadCaptions} />
            </div>
          ) : (
            <div className="live-cap-settings-icon">
              {meetingFeatures?.voice_text_translation === 1 && (
                <>
                  <Popover
                    content={popoverContent}
                    overlayClassName="choose-live-cap"
                    placement="bottomRight"
                    align={{ offset: [15, 0] }}
                    // trigger={["click"]}
                  >
                    <LanguageSelectionIco2
                      onClick={() => setIsModalVisible(true)}
                    />
                  </Popover>
                  <Popconfirm
                    placement="bottomRight"
                    title={text}
                    overlayClassName="live-cap-settings-pop-confirm"
                    okText={
                      <TfiClose
                        style={{
                          width: "12px",
                          height: "12px",
                          color: "black",
                          backgroundColor: "white",
                        }}
                      />
                    }
                    okButtonProps={{
                      style: {
                        // display: "none",
                        backgroundColor: "white",
                        border: "none",
                        borderRadius: "50%",
                        width: "20px",
                        height: "20px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        position: "absolute",
                        top: "0",
                        right: "0",
                        padding: "0",
                        boxShadow: "none",
                      },
                    }}
                    // showOk={false}
                    showCancel={false}
                    open={tip}
                    onConfirm={() => setTip(false)}
                    onCancel={() => setTip(true)}
                    align={{ offset: [17, 24] }}
                  >
                    {/* <IoMdCloseCircle className="live-cap-settings-pop-confirm-close" /> */}
                    {/* <Button>TL</Button> */}
                  </Popconfirm>
                </>
              )}
            </div>
          )}
          {/* {(isHost || isCoHost) && (
            <DownloadCaptions onClick={downloadCaptions} />
          )} */}
          <Modal
            title="Choose Language"
            open={isModalVisible}
            onOk={() => {
              setIsModalVisible(false);
            }}
            onCancel={() => {
              setIsModalVisible(false);
            }}
            className="live-cap-settings-modal"
            footer={null}
          >
            <LanguageSelection
              handleLanguageSelection={handleSelectedLanguage}
              className="live-cap-settings-modal-language-selection"
            />
          </Modal>
        </div>
      ) : (
        <>
          <LanguageSelectionIco />
          <p>Transcription Language</p>
        </>
      )}
    </div>
  );
}
