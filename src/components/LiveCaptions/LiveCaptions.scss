@import "../../styles/variables";
.live-captions{
    .sd-container-inner{
        .sd-header{
            background-color: #242424 !important;
            border-radius: 8px;
            padding: 8px;
            .sd-title{
                padding: 0 !important;
            }
            .sd-button{
                background-color: #393939 !important;
                border-radius: 50% !important;
                padding: 4px;
            }
        }
        .sd-container-below{
            overflow: hidden;
        }
        >hr{
            display: none;
        }
    }
    &-title{
        margin-left: 8px;
        display: flex;
        width: 100%;
        gap: 10px;
        // justify-content: space-between;
        align-items: center;
        p{
            margin: 0;
            font-size: 16px;
        }
        >h2{
            font-size: 1rem;
            margin: 0;
            line-height: 32px;
        }
        &-live{
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            h2{
                margin: 0;
                line-height: 32px;
                font-family: Segoe UI;
                font-size: 20px;
                font-weight: 700;
                color: white;
            }
        }
    }
}
.live-cap{
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    &-out{
        position: relative;
        height: 100%;
        width: 100%;
        overflow-y: auto;
        &::-webkit-scrollbar{
            width: 5px;
        }
        &::-webkit-scrollbar-thumb{
            background-color: #454545;
            border-radius: 10px;
        }
        &::-webkit-scrollbar-track{
            background-color: #000;
        }
    }
    &-message{
        display: flex;
        width: 100%;
        justify-content: space-between;
        &-avt{
            display: flex;
            gap: 0.5rem;
            p{
                margin: 0;
            }
        }
    }
    >span{
        padding: 4px 8px;
    }
    &-choose-lang{
        display: flex;
        flex-direction: column;
        background-color: #242424;
        margin-top: 1rem;
        padding: 1rem 0;
        border-radius: 10px;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        &::-webkit-scrollbar{
            width: 0px;
        }
        span{
            font-family: $font;
            font-size: 16px;
            font-weight: 200;
            padding: 0.8rem 1rem;
            cursor: pointer;
            width: 96%;
            &:hover{
                background-color: #3c3c3c;
            }
        }
        .language-search-box{
            margin: 0.1rem 1rem;
            width: auto;
            background-color: #393939;
            border-radius: 4px;
            border: none;
            color: white;
            font-family: $font;
            padding: 0;
            input{
                width: 100%;
                border-radius: 4px 0 0 4px;
                padding: 0.1rem 0.3rem !important;
                font-weight: 400;
                background-color: #393939;
                color: white;
            }
            .ant-input-suffix{
                width: 1.3rem;
                padding: 0;
                display: flex;
                align-items: center;
                .ant-input-clear-icon{
                    width: auto;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    .anticon-close-circle{
                        padding: 0;
                        svg{
                            color: rgb(206, 137, 137);
                            transition: all 0.3s;
                            &:hover{
                                color: #9b9b9b;
                            }
                        }
                    }
                }
            }
        }
        .language-options{
            overflow-x: hidden;
            overflow-y: auto;
            margin-right: 0.2rem;
            &::-webkit-scrollbar{
                width: 5px;
            }
            &::-webkit-scrollbar-thumb{
                background-color: #454545;
                border-radius: 10px;
            }
            &::-webkit-scrollbar-track{
                background-color: #000;
                border-radius: 10px;
            }
        }
    }
    &-settings{
        width: auto;
        // margin-left: 1rem;
        // padding-left: 1rem;
        &-pop-confirm{
            // position: relative;
            z-index: 100 !important;
            &-close{
                position: absolute;
                top: 2rem;
                right: -0.5rem;
                color: red;
                z-index: 101;
            }
            .ant-popover-inner{
                width: fit-content;
                &-content{
                    padding: 12px 24px;
                    .ant-popover-buttons{
                        margin: 0;
                    }
                }
            }
            .ant-popover-message{
                padding: 0;
                .ant-popover-message-title{
                    font-family: $font;
                    font-size: 13px;
                    font-weight: 600;
                }
                .ant-popover-message-icon{
                    display: none;
                }
            }
        }
        &-icon{
            margin-right: 0.8rem;
            cursor: pointer;
            width: 29px;
            height: 29px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            svg{
                width: 100%;
                height: auto;
            }
            &-host{
                margin: 0;
                display: flex;
                width: auto;
                svg {
                    width: 100;
                    // color: black;
                }
            }
        }
        .ant-popover-inner{
            border-radius: 10px;
            &-content{
                // background-color: #242424;
                border-radius: 8px;
                padding: 1rem 0;
            }
        }
        &-items{
            display: flex;

width: auto;
svg {
    width: 20px;
                    // color: black;
}            flex-direction: column;
            width: auto;
            &-item{
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.5rem 1rem;
                cursor: pointer;
                width: max-content;
                svg{
                    width: 18px;
                    color: black;
                }
                p{
                    width: -webkit-fill-available;
                    // color: white;
                    margin: 0;
                    font-family: Segoe UI;
                    font-size: 16px;
                    font-weight: 400;
                }
                &:hover{
                    background-color: #3c3c3c;
                    color: white;
                }
            }
        }
        &-modal{
            width: 22rem !important;
            max-height: 80vh;
            position: relative;
            .ant-modal-content{
                border-radius: 10px;
                background-color: #000;
            }
            .ant-modal{
                &-header{
                    // background-color: #242424;
                    border-radius: 10px 10px 0 0;
                }
                &-title{
                    // color: white;
                    font-weight: bold;
                }
                &-body{
                    padding: 0;
                    border-radius: 0 0 10px 10px !important;
                }
                &-footer{
                    background-color: #242424;
                    border-radius: 0 0 10px 10px;
                    .ant-btn{
                        background-color: #393939;
                        border-radius: 4px;
                        color: white;
                        font-family: $font;
                        font-weight: 400;
                        &:hover{
                            background-color: #3c3c3c;
                        }
                        &-primary{
                            background-color: #0d6efd;
                            border-radius: 4px;
                            color: white;
                            font-family: $font;
                            font-weight: 400;
                            &:hover{
                                background-color: #3c3c3c;
                            }
                        }
                    }
                }
            }
            &-language-selection{
                margin: 0;
                border-radius: 0;
                height: 65vh;
                overflow: auto;
                background-color: #fff;
                border-radius: 0 0 10px 10px;
                .language-options{
                    span{
                        color: black;
                        font-weight: 400;
                        &:hover{
                            color: white;
                        }
                    }
                    &::-webkit-scrollbar{
                        width: 5px;
                    }
                    &::-webkit-scrollbar-thumb{
                        border-radius: 10px;
                        background-color: #000;
                    }
                    &::-webkit-scrollbar-track{
                        background-color: #dadada;
                    }
                }
                .language-search-box{
                    background-color: #e7e7e7;
                    input{
                        background-color: #e7e7e7;
                        color: black;
                        border: 1px solid black;
                        border-right: none;
                        &::placeholder{
                            color: rgb(166, 166, 166);
                        }
                        &:hover{
                            .ant-input-suffix{
                                background-color: #dadada;
                            }
                        }
                    }
                    .ant-input-suffix{
                        background-color: #e7e7e7;
                        border: 1px solid black;
                        border-left: none;
                        margin: 0;
                        border-radius: 0 4px 4px 0;
                        .ant-input-clear-icon{
                            &:hover{
                                background-color: #dadada;
                            };
                            .anticon-close-circle{
                                &:hover{
                                    background-color: #dadada;
                                };

                            }
                        }
                    }
                }
            }
        }
    }
}
.live-cap{
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0.2rem;
    &-per{
        display: flex;
        flex-direction: column;
        gap: 0.2rem;
        .translate-to{
            font-size: 12px;
            font-weight: 300;
            display: flex;
            justify-content: flex-end;
            width: auto;
            svg{
                cursor: pointer;
                width: 25px;
                height: 25px;
            }
        }
        &-final-text{
            font-family: "Inter", sans-serif;
            font-size: 1.1rem;
            font-weight: 300;
            padding: 0 0.5rem;
            // font-style: italic;
        }
        p{
            margin: 0;
            span{
                font-size: 19px;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        &-head{
            display: flex;
            justify-content: space-between;
            align-items: center;
            &-name{
                font-family: Segoe UI;
                font-size: 15px;
                font-weight: 400;
                text-align: left;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
            &-time{
                color: #D9D9D9;
                font-size: 12px;
            }
        }
    }
}
.choose-live-cap{
    .ant-popover-inner-content{
        padding: 2px 8px;
    }
    .ant-popover-inner{
        border-radius: 6px;
    }
}