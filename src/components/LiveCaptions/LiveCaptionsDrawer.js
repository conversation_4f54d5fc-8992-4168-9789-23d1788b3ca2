/* eslint-disable no-unused-vars */
import React, { useEffect, useMemo } from "react";
import SideDrawer from "../SideDrawer";
import "./LiveCaptions.scss";
import { DataReceivedEvent } from "../../utils/constants";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import LiveCaptionsTitle from "./LiveCaptionsTitle";
import LiveCaptionsMessages from "./LiveCaptionsMessages";
import LanguageSelection from "./LanguageSelection";
import { parseMetadata } from "../../utils/helper";

export default function LiveCaptionsDrawer({
  isLiveCaptionsDrawerOpen,
  setIsLiveCaptionsDrawerOpen,
  remoteParticipants,
  localParticipant,
  liveCaptionData,
  meetingDetails,
  livecaptionsobject,
  setlivecaptionsobject,
  setfinalcaptions,
  finalcaptions,
  isWhiteboardOpen,
  id,
  setDrawerState,
  translationDetails,
  setTranslationDetails,
  finalTranslatedCaptions,
  setFinalTranslatedCaptions,
  meetingFeatures,
  setToastNotification,
  setToastStatus,
  setShowToast,
  isHost,
  coHostToken,
  apiToken,
  devMode,
}) {
  // Function to handle language selection
  const handleLanguageSelection = async (langId, language) => {
    const newLiveCaptionsObject = {
      ...livecaptionsobject,
      langCode: langId,
      isLanguageSelected: true,
    };

    try {
      const response = await SettingsMenuServices.setLiveTranscriptionDetail(
        id,
        langId,
        language,
        true,
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
      if (response.success === 0) {
        setToastNotification("Error setting live transcription detail");
        setToastStatus("error");
        setShowToast(true);
        throw new Error("Error setting live transcription detail");
      } else if (response.success === 1) {
        setToastNotification(`Live transcription language set to ${language}`);
        setToastStatus("success");
        setShowToast(true);
        setlivecaptionsobject(newLiveCaptionsObject);
      }
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.SHOW_LIVECAPTION,
          // livecaptionsdata: newLiveCaptionsObject,
          liveCaptionsData: {
            ...newLiveCaptionsObject,
            source_lang: langId, // Include source_lang
          },
        })
      );

      localParticipant.publishData(data, {
        reliable: true,
      });
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.error("Error stringifying object:", error);
    }
  };

  // Function to generate timestamp
  const timeStamp = useMemo(() => {
    return () => {
      const now = new Date();
      let hours = now.getHours();
      const minutes = now.getMinutes().toString().padStart(2, "0");
      const ampm = hours >= 12 ? "PM" : "AM";
      hours %= 12;
      hours = hours.toString().padStart(2, "0");
      return `${hours}:${minutes} ${ampm}`;
    };
  }, []);

  // Download captions
  const downloadCaptions = () => {
    const textContent = finalcaptions
      .map((caption) => `${caption.name} ${caption.time}\n${caption.text}\n\n`)
      .join("");
    const blob = new Blob([textContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "captions.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  useEffect(() => {
    setTranslationDetails({
      ...translationDetails,
      source_lang: livecaptionsobject.langCode,
    });
  }, [livecaptionsobject]);

  const fetchTranslatedText = async (textData, sourceLang, targetLang) => {
    try {
      if (!textData || !targetLang) {
        console.log("Provide both text data and target language.");
        return null;
      }
  
      let bodyData = {
        text: textData,
        source_language: sourceLang,
        target_language: targetLang,
        meeting_uid: id,
      };
      const res = await SettingsMenuServices.translateTextService(bodyData, null, devMode);
      const { success, message, data } = res;

      if (success === 1 && message === "success") {
        return {
          translatedText: data?.translatedText,
        };
      } else {
        setToastNotification("Translation failed.");
        setToastStatus("error");
        setShowToast(true);
        return null;
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      throw error;
    }
  };

  const getTranslatedData = async (textData) => {
    const lastCaption = finalcaptions[finalcaptions.length - 1];

    try {
      const translationResult = await fetchTranslatedText(
        textData,
        translationDetails?.source_lang,
        translationDetails?.target_lang,
      );

      if (translationResult) {
        const { translatedText } = translationResult;
  
        setFinalTranslatedCaptions((prevCaptions) => [
          ...prevCaptions,
          {
            name: lastCaption?.name,
            time: lastCaption?.time,
            text: translatedText,
            targetLang: translationDetails.target_lang,
            sourceLang: translationDetails.source_lang,
          },
        ]);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  useEffect(() => {
    const lastCaption = finalcaptions[finalcaptions.length - 1];
    if(translationDetails.target_lang !== ""){
      getTranslatedData(lastCaption?.text);
    }
  }, [finalcaptions]);

  const translateSelctedText = async (text) => {
    const findTextIndex = finalTranslatedCaptions.findIndex((caption) => caption.text === text);
    const selectedText = finalTranslatedCaptions[findTextIndex];
    const extractedText = selectedText.text;
    const sourceLanguage = selectedText.targetLang || selectedText.sourceLang;
    const targetLanguage = translationDetails.target_lang;

    try {
      const translationResult = await fetchTranslatedText(
        extractedText,
        sourceLanguage,
        targetLanguage
      );

      if (translationResult) {
        const { translatedText } = translationResult;

        selectedText.text = translatedText;
        selectedText.targetLang = targetLanguage;
        selectedText.sourceLang = sourceLanguage;
        setFinalTranslatedCaptions((prevCaptions) => [
          ...prevCaptions.slice(0, findTextIndex),
          selectedText,
          ...prevCaptions.slice(findTextIndex + 1),
        ]);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  }

  return (
    <SideDrawer
      className="live-captions"
      isWhiteboardOpen={isWhiteboardOpen}
      title={
        <LiveCaptionsTitle
          isLanguageSelected={livecaptionsobject.isLanguageSelected}
          handleLanguageSelection={handleLanguageSelection}
          downloadCaptions={downloadCaptions}
          isHost={
            parseMetadata(localParticipant.metadata)?.role_name === "moderator"
          }
          isCoHost={
            parseMetadata(localParticipant.metadata)?.role_name === "cohost"
          }
          setTranslationDetails={setTranslationDetails}
          translationDetails={translationDetails}
          meetingFeatures={meetingFeatures}
        />
      }
      show={isLiveCaptionsDrawerOpen}
      setShow={setIsLiveCaptionsDrawerOpen}
      isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
      style={{ gap: "1rem" }}
      setDrawerState={setDrawerState}
    >
      {parseMetadata(localParticipant.metadata)?.role_name === "moderator" ||
      parseMetadata(localParticipant.metadata)?.role_name === "cohost" ? (
        livecaptionsobject.isLanguageSelected ? (
          <LiveCaptionsMessages
            isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
            liveCaptionData={liveCaptionData}
            meetingDetails={meetingDetails}
            localParticipant={localParticipant}
            remoteParticipants={remoteParticipants}
            finalCaptions={finalcaptions}
            setFinalCaptions={setfinalcaptions}
            langCode={livecaptionsobject.langCode}
            timeStamp={timeStamp}
            translationDetails={translationDetails}
            setToastNotification={setToastNotification}
            setToastStatus={setToastStatus}
            setShowToast={setShowToast}
            isHost={
              parseMetadata(localParticipant.metadata)?.role_name === "moderator"
            }
            isCoHost={
              parseMetadata(localParticipant.metadata)?.role_name === "cohost"
            }
            apiToken={apiToken}
            coHostToken={coHostToken}
            devMode={devMode}
            finalTranslatedCaptions={finalTranslatedCaptions}
          />
          ) : (
            <LanguageSelection
            handleLanguageSelection={handleLanguageSelection}
            />
          )
      ) : (
        <LiveCaptionsMessages
          isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
          liveCaptionData={liveCaptionData}
          meetingDetails={meetingDetails}
          localParticipant={localParticipant}
          remoteParticipants={remoteParticipants}
          finalCaptions={finalcaptions}
          setFinalCaptions={setfinalcaptions}
          langCode={livecaptionsobject.langCode}
          timeStamp={timeStamp}
          finalTranslatedCaptions={finalTranslatedCaptions}
          translationDetails={translationDetails}
          translateSelctedText={translateSelctedText}
          setToastNotification={setToastNotification}
          setToastStatus={setToastStatus}
          setShowToast={setShowToast}
          isHost={
            parseMetadata(localParticipant.metadata)?.role_name === "moderator"
          }
          isCoHost={
            parseMetadata(localParticipant.metadata)?.role_name === "cohost"
          }
          apiToken={apiToken}
          coHostToken={coHostToken}
          devMode={devMode}
        />
      )}
    </SideDrawer>
  );
}
