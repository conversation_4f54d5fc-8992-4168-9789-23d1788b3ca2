import React, { useEffect, useRef, useState } from "react";
import { Avatar } from "antd";
import { generateAvatar } from "../../utils/helper";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
// import { ReactComponent as TranslateIcon } from "./Assets/Language.svg";
import { ReactComponent as LanguageSelectionIco2 } from "./Assets/ic_translate_chats_colored.svg";

export default function LiveCaptionsMessages({
  isLiveCaptionsDrawerOpen,
  liveCaptionData,
  meetingDetails,
  localParticipant,
  remoteParticipants,
  finalCaptions,
  setFinalCaptions,
  timeStamp,
  // langCode,
  translationDetails,
  finalTranslatedCaptions,
  translateSelctedText,
  isHost,
  coHostToken,
  apiToken,
  // langCode,
  devMode,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  // State for managing captions
  const [currentPartial, setCurrentPartial] = useState("");
  // const [finalCaptions, setFinalCaptions] = useState([]);
  const [currentSpeaker, setCurrentSpeaker] = useState("");

  // Function to start the transcription
  const startTranscription = async () => {
    try {
      const response = await SettingsMenuServices.startLiveTranscription(
        meetingDetails?.room_uid,
        isHost ? apiToken:coHostToken,
        localParticipant?.participantInfo,
        devMode,
      );
      if (response?.success === 0) {
        console.log("Error in Transcription API.", response);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.log("Error in Transcription API:", error);
    }
  };

  // Effect to start transcription and update captions when the drawer is open
  useEffect(() => {
    let interval;

    if (isLiveCaptionsDrawerOpen) {
      startTranscription(); // Start the transcription immediately
      interval = setInterval(() => {
        startTranscription();
      }, 30000); // 30000 ms = 30 seconds
    }

    // Cleanup function to clear the interval when the component unmounts or drawer closes
    return () => {
      clearInterval(interval);
    };
  }, [isLiveCaptionsDrawerOpen]);

  // Effect to update captions
  useEffect(() => {
    if (liveCaptionData) {
      let participantName = "";

      // First, check if the local participant is the speaker
      if (
        liveCaptionData?.participant_identity === localParticipant?.identity
      ) {
        participantName = localParticipant?.name;
      } else {
        // If not, check among the remote participants
        for (let [key, value] of remoteParticipants.entries()) {
          if (liveCaptionData?.participant_identity === key) {
            participantName = value.name;
            break; // Exit loop once the matching participant is found
          }
        }
      }

      if (liveCaptionData.final) {
        // Add final caption and participant name to the array
        setFinalCaptions((prev) => [
          ...prev,
          {
            name: participantName,
            text: liveCaptionData.final,
            time: timeStamp(),
            sourceLang: translationDetails?.source_lang,
            targetLang: translationDetails?.target_lang === "" ? translationDetails?.source_lang : translationDetails?.target_lang,
          },
        ]);
        setCurrentPartial(""); // Clear the current partial text
      } else if (liveCaptionData.partial) {
        // Update current partial text
        setCurrentSpeaker(participantName);
        setCurrentPartial(liveCaptionData.partial);
      }
    }
  }, [liveCaptionData, localParticipant, remoteParticipants]);

  let scrollRef = useRef(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [currentPartial, finalCaptions]);

  return (
    <div className="live-cap-out" ref={scrollRef}>
      <div className="live-cap" ref={scrollRef}>
        {translationDetails?.target_lang === "" ? (
          finalCaptions.map((caption, index) => (
            <div key={index} className="live-cap-per">
              <div className="live-cap-per-head">
                <p className="live-cap-per-head-name">
                  <Avatar
                    style={{
                      backgroundColor: "#fd4563",
                      verticalAlign: "middle",
                    }}
                  >
                    {generateAvatar(caption.name)}
                  </Avatar>
                  {caption.name}
                </p>
                <p className="live-cap-per-head-time">{caption.time}</p>
              </div>
              <p className="live-cap-per-final-text">{caption.text}</p>
            </div>
          ))
        ) : (
          finalTranslatedCaptions.map((caption, index) => (
            <div key={index} className="live-cap-per">
              <div className="live-cap-per-head">
                <p className="live-cap-per-head-name">
                  <Avatar
                    style={{
                      backgroundColor: "#fd4563",
                      verticalAlign: "middle",
                    }}
                  >
                    {generateAvatar(caption.name)}
                  </Avatar>
                  {caption.name}
                </p>
                <p className="live-cap-per-head-time">{caption.time}</p>
              </div>
              <p className="live-cap-per-final-text">{caption.text}</p>
              {caption.targetLang !== translationDetails?.target_lang && (
                <div className="translate-to" >
                  <LanguageSelectionIco2
                    onClick={() => {
                      translateSelctedText(caption.text);
                    }}
                  />
                </div>
              )}
            </div>
          ))
        )}

        {/* Display current speaker and partial text */}
        {currentPartial && (
          <div className="live-cap-per">
            <div className="live-cap-per-head">
              <p className="live-cap-per-head-name">
                <Avatar
                  style={{
                    backgroundColor: "#fd4563",
                    verticalAlign: "middle",
                  }}
                >
                  {generateAvatar(currentSpeaker)}
                </Avatar>
                {currentSpeaker}
              </p>
              <p className="live-cap-per-head-time">{timeStamp()}</p>
            </div>
            <p className="live-cap-per-final-text">{currentPartial}</p>
          </div>
        )}
      </div>
    </div>
  );
}
