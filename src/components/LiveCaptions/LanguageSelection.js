import React, { useState } from "react";
import { Input } from "antd";
import LanguagesList from "./LiveCaptionLanguages.json";

export default function LanguageSelection({ handleLanguageSelection, className }) {
  const [searchTerm, setSearchTerm] = useState(""); // State for search input

  // Function to handle search input changes
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const filteredLanguages = LanguagesList.filter(
    (language) =>
      language.language.toLowerCase().includes(searchTerm.toLowerCase()) ||
      language.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className={`live-cap-choose-lang ${className}`}>
      <Input
        placeholder="Search language..."
        value={searchTerm}
        onChange={handleSearch}
        style={{ marginBottom: "1rem" }}
        className="language-search-box"
        allowClear
      />
      {/* Render filtered languages */}
      <div className="language-options">
        {filteredLanguages.map((language, index) => {
          const codeAlpha = `${language.code}`; // Create a key for the language code
          return (
            <span
              key={index} // Use a unique key for each element
              onClick={() => handleLanguageSelection(codeAlpha, language.language)} // Pass the language code on click
              style={{ cursor: "pointer", display: "inline-block", margin: "0 5px" }} // Optional styling
            >
              {language.language}
            </span>
          );
        })}
      </div>
    </div>
  );
}
