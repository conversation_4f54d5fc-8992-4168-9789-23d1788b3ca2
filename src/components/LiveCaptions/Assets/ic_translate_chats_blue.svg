<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 240 240">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6, .cls-7, .cls-8, .cls-9, .cls-10, .cls-11 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#linear-gradient-6);
      }

      .cls-3 {
        fill: url(#linear-gradient-5);
      }

      .cls-4 {
        fill: none;
      }

      .cls-5 {
        fill: url(#linear-gradient-2);
      }

      .cls-6 {
        fill: url(#linear-gradient-7);
      }

      .cls-7 {
        fill: url(#linear-gradient-3);
      }

      .cls-8 {
        fill: url(#linear-gradient-9);
      }

      .cls-9 {
        fill: url(#linear-gradient-8);
      }

      .cls-10 {
        fill: url(#linear-gradient-10);
      }

      .cls-11 {
        fill: url(#linear-gradient-4);
      }
    </style>
    <linearGradient id="linear-gradient" x1="-339.4" y1="-120.67" x2="-339.4" y2="-110.67" gradientTransform="translate(6907.92 2433.4) scale(20)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3970b8"/>
      <stop offset=".34" stop-color="#38439b"/>
      <stop offset=".76" stop-color="#383c98"/>
      <stop offset="1" stop-color="#373594"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-155.4" y1="-435.57" x2="-155.4" y2="-425.57" gradientTransform="translate(581.99 1411.88) scale(2.6 3)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-3" x1="-176.65" y1="-405.13" x2="-176.65" y2="-395.13" gradientTransform="translate(688.63 1426.52) scale(2.89 3.27)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="-209.72" y1="-486.39" x2="-209.72" y2="-476.39" gradientTransform="translate(784.88 1388.21) scale(3.17 2.52)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-5" x1="-156.73" y1="-551.87" x2="-156.73" y2="-541.88" gradientTransform="translate(489.39 1367.49) scale(2.36 2.28)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-6" x1="-176.79" y1="-503.76" x2="-176.79" y2="-493.77" gradientTransform="translate(580.88 1382.47) scale(2.61 2.53)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-7" x1="-92.52" y1="-618.46" x2="-92.52" y2="-608.45" gradientTransform="translate(282.05 1352.99) scale(1.79 2.09)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-8" x1="-137.78" y1="-559.35" x2="-137.78" y2="-549.35" gradientTransform="translate(412.67 1368.02) scale(2.15 2.34)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-9" x1="-190.96" y1="-474.21" x2="-190.96" y2="-464.21" gradientTransform="translate(545.98 1392.03) scale(2.53 2.71)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-10" x1="-206.58" y1="-437.01" x2="-206.58" y2="-427.01" gradientTransform="translate(637.1 1409.5) scale(2.78 2.98)" xlink:href="#linear-gradient"/>
  </defs>
  <g id="ic_translate_colored" data-name="ic translate colored">
    <rect id="Rectangle_14734" data-name="Rectangle 14734" class="cls-4" width="240" height="240"/>
    <g id="chat">
      <g id="Layer_8" data-name="Layer 8">
        <g id="Group_6853" data-name="Group 6853">
          <circle id="Ellipse_2712" data-name="Ellipse 2712" class="cls-4" cx="119.99" cy="119.99" r="100"/>
          <path id="Ellipse_2712_-_Outline" data-name="Ellipse 2712 - Outline" class="cls-1" d="M119.99,29.99c-49.71,0-90,40.29-90,90s40.29,90,90,90,90-40.29,90-90c-.06-49.68-40.32-89.94-90-90M119.99,19.99c55.23,0,100,44.77,100,100s-44.77,100-100,100S19.99,175.22,19.99,119.99,64.76,19.99,119.99,19.99Z"/>
          <path id="Path_6100" data-name="Path 6100" class="cls-5" d="M187.79,132.91l2.36-1.33.79,1.83c-.55.43-1.17.77-1.83,1-.48.21-1,.31-1.52.3-.5.04-1-.16-1.33-.54-.31-.42-.47-.94-.43-1.46v-6.41c-.49.22-1,.38-1.52.49-.5.12-1.01.18-1.52.18-.77,0-1.53-.14-2.24-.43-.71-.31-1.34-.78-1.83-1.37h-.06c-.31,1.36-1.01,2.6-2,3.58-.97.85-2.23,1.29-3.52,1.21-.68,0-1.36-.11-2-.36-.6-.24-1.16-.57-1.64-1-.53-.48-.99-1.03-1.37-1.64-.43-.67-.79-1.37-1.21-2.18h-.06c-.63,0-1.27-.07-1.88-.24l.3-1.64c.48.07.97.11,1.46.12.6.02,1.19-.06,1.76-.24.5-.18.96-.45,1.37-.79.39-.38.7-.83.92-1.33.2-.61.3-1.24.3-1.88s-.1-1.24-.3-1.83c-.17-.5-.47-.96-.85-1.33-.36-.35-.79-.62-1.27-.79-.55-.2-1.12-.3-1.7-.3-.23-.01-.45,0-.67.06-.25,0-.49.05-.73.12l-.36-1.7c.64-.14,1.29-.22,1.94-.24.85,0,1.7.16,2.49.49.69.32,1.31.77,1.83,1.33.49.57.86,1.23,1.09,1.94.24.72.36,1.48.36,2.24,0,.68-.1,1.35-.3,2-.18.59-.47,1.15-.85,1.64-.35.49-.81.91-1.33,1.21-.53.34-1.1.6-1.7.79.43,1.04,1.07,1.97,1.88,2.75.64.6,1.48.93,2.36.92.53,0,1.05-.12,1.52-.36.53-.28.98-.67,1.33-1.15.41-.63.72-1.33.92-2.06.26-1.03.38-2.1.36-3.16v-3h1.88v2.29c0,.74.09,1.47.3,2.18.18.5.47.95.85,1.33.33.3.72.53,1.15.67.41.12.84.18,1.27.18,1.14-.04,2.24-.42,3.16-1.09v-10.89h1.88v19.89h.19ZM178.45,104.71c.75-.01,1.5.05,2.24.18.59.1,1.16.28,1.7.54.47.26.9.6,1.27,1,.38.42.71.88,1,1.37l3.09,5.34-1.7.67-2.24-4.43c-.27-.49-.6-.94-1-1.33-.33-.35-.72-.64-1.15-.85-.46-.22-.95-.36-1.46-.43-.58-.07-1.17-.11-1.76-.12h-3.44v-1.94s3.45,0,3.45,0Z"/>
          <path id="Path_6100_-_Outline" data-name="Path 6100 - Outline" class="cls-7" d="M187.47,136c-.83,0-1.61-.37-2.15-1l-.06-.08c-.48-.65-.72-1.44-.67-2.24v-4.67h0c-.57.13-1.16.2-1.75.21h-.1c-.9,0-1.8-.18-2.63-.52-.29-.13-.57-.27-.84-.44-.43.89-1,1.69-1.7,2.38h0c-1.2,1.06-2.78,1.61-4.38,1.53h0c-.82,0-1.63-.15-2.39-.45-.73-.28-1.41-.68-2-1.19-.63-.56-1.17-1.21-1.62-1.93-.33-.51-.62-1.05-.92-1.62-.51-.04-1.02-.13-1.51-.27l-1.1-.31.73-3.94,1.18.16c.44.06.89.1,1.33.11h.15c.4,0,.81-.07,1.19-.19.33-.12.64-.3.91-.52.24-.25.44-.54.59-.86.15-.46.22-.94.22-1.42s-.08-.98-.24-1.44c-.11-.31-.29-.6-.53-.83-.23-.23-.51-.4-.82-.51-.41-.15-.85-.23-1.29-.23h-.11c-.09-.01-.19-.01-.28,0h-.23c-.15,0-.29.03-.43.07l-1.29.38-.9-4.24,1.22-.26c.71-.15,1.43-.24,2.16-.27h.12c1,0,1.98.2,2.9.58.86.39,1.63.95,2.27,1.64.61.7,1.08,1.52,1.36,2.41.28.85.42,1.73.42,2.62,0,.8-.12,1.6-.36,2.37-.22.72-.55,1.4-1,2-.45.63-1.03,1.15-1.7,1.54-.19.12-.38.23-.58.34.29.44.62.85,1,1.22.39.37.91.57,1.45.57h.06c.32,0,.64-.07.93-.22.34-.18.64-.44.87-.75.33-.52.58-1.09.74-1.68.23-.92.34-1.86.32-2.81v-4.3h4.38v3.54c0,.61.07,1.21.24,1.8.12.31.29.59.52.82.2.18.43.31.68.39.29.08.59.12.89.12.67-.03,1.32-.21,1.9-.53v-9.87l-1.75-3.45c-.2-.37-.46-.7-.75-1-.22-.25-.49-.45-.79-.59-.35-.16-.71-.27-1.09-.32-.53-.07-1.07-.1-1.6-.11h-4.68v-4.42h4.79c.77,0,1.55.07,2.31.2.69.12,1.37.34,2,.65.61.32,1.16.73,1.63,1.23.45.49.86,1.02,1.2,1.59l2.56,4.42.53-.21v1.14l.73,1.26-.74.29v16.83l1.89-1.07,1.71,4-.73.6c-.65.52-1.38.94-2.16,1.22-.6.26-1.25.39-1.9.39h-.2l-.11.14h0Z"/>
          <path id="Path_6099" data-name="Path 6099" class="cls-11" d="M104.19,162.52h31.73v4.2h-3.59v21h-3.31l-.31-.79c-.25-.63-.55-1.25-.89-1.84-.32-.57-.68-1.12-1.07-1.64-.39-.51-.81-.99-1.26-1.45-.22-.22-.45-.43-.68-.64-.63.54-1.34.99-2.1,1.33-1.25.51-2.58.78-3.93.78h-.11c-1.58,0-3.14-.34-4.58-1-1.55-.73-2.93-1.77-4.05-3.06-1.29-1.45-2.35-3.09-3.16-4.85-.97-2.1-1.71-4.3-2.21-6.56l-.19-.88.35-.21h-.61s-.03-4.39-.03-4.39ZM127.78,166.73h-4.23c.67.38,1.27.86,1.79,1.42.73.81,1.31,1.74,1.7,2.76.43,1.12.64,2.3.63,3.5,0,.71-.07,1.41-.21,2.11-.07.41-.18.8-.32,1.19l.64.62v-11.6h0ZM113.2,166.88h-4.31c.48,1.87,1.15,3.69,2,5.43.66,1.41,1.5,2.73,2.5,3.93.73.84,1.63,1.52,2.63,2,.86.39,1.79.59,2.73.6h.12c.61,0,1.21-.11,1.78-.34.53-.21,1-.52,1.4-.93.39-.38.68-.85.86-1.36.23-.59.35-1.22.36-1.86.01-.57-.08-1.14-.28-1.67-.16-.48-.42-.93-.75-1.32-.11-.13-.23-.24-.36-.35v.25c0,.51-.08,1.01-.26,1.48-.18.48-.46.91-.82,1.28l-.05.05c-.33.3-.7.56-1.09.78h-.1c-.49.22-1.02.34-1.56.35h-.12c-.55,0-1.09-.12-1.59-.36-.51-.23-.96-.57-1.32-1-.37-.39-.66-.85-.85-1.35-.24-.54-.35-1.13-.33-1.72,0-.72.17-1.44.49-2.09.11-.25.25-.48.41-.7l-.42-.39c-.34-.26-.69-.5-1.06-.72h-.01ZM119.02,172.3c.11-.12.19-.26.25-.41.07-.18.1-.37.1-.56,0-.21-.04-.42-.11-.62-.1-.29-.23-.56-.4-.81l-.41-.61.53-1.06-1,.24-.89.68c-.25.2-.46.45-.59.74-.16.31-.24.65-.25,1-.01.24.03.48.13.7v.1c.07.19.18.37.32.51l.05.06c.13.15.28.26.46.34h.05c.17.08.35.12.53.12h.05c.2,0,.39-.05.57-.12.21-.08.42-.18.61-.31h0Z"/>
          <path id="Path_6098" data-name="Path 6098" class="cls-3" d="M118.28,116.27c.06-2.15-1.63-3.95-3.79-4.01h-.4c-2.2-.21-4.15,1.41-4.35,3.61,0,.09-.01.18-.02.27h-1.83c0-3.08,2.51-5.56,5.59-5.55.2,0,.4.01.59.03,2.96-.33,5.64,1.8,5.97,4.77.02.19.03.38.03.57.09,2.31-1.54,4.34-3.82,4.73v.06c.55.08,1.08.25,1.58.49h7.28v-10.31h6.19v1.64h-4.3v20.8h-1.88v-10.43h-5.5c1.72,2.68.94,6.24-1.73,7.96-1.04.66-2.26.98-3.49.9-3.35.33-6.34-2.13-6.66-5.48-.02-.19-.03-.38-.03-.57h1.88c-.04,2.41,1.89,4.4,4.3,4.44h.37c2.27.17,4.25-1.52,4.42-3.79.01-.17.02-.34,0-.51,0-1.08-.41-2.12-1.15-2.91h0v-.06c-.94-.87-2.18-1.33-3.46-1.27h-1.15v-1.64h1.15c2.1.23,4-1.28,4.23-3.39.01-.11.02-.22.02-.32l-.05-.03h.01Z"/>
          <path id="Path_6098_-_Outline" data-name="Path 6098 - Outline" class="cls-2" d="M113.46,109.35h.61c3.66-.3,6.87,2.42,7.18,6.08.01.17.02.34.02.5.05,1.47-.46,2.9-1.43,4h4v-10.26h8.69v4.14h-4.3v20.8h-4.38v-10.43h-2.33c1.03,3.73-1.15,7.58-4.88,8.62-.73.2-1.48.28-2.23.24-4.04.36-7.61-2.63-7.97-6.67-.02-.21-.03-.42-.03-.63v-1.25h4.41v1.27c-.03,1.72,1.35,3.14,3.07,3.17h.44c1.58.12,2.96-1.06,3.08-2.64,0-.12.01-.24,0-.36v-.06c0-.76-.29-1.49-.81-2.05l-.23-.24c-.65-.49-1.45-.74-2.26-.7h-2.46v-4.14h2.54c1.42.16,2.69-.87,2.85-2.28,0-.07.01-.14.01-.22.04-1.46-1.11-2.68-2.57-2.73h-.49c-1.49-.16-2.82.92-2.98,2.4,0,.08-.01.17-.02.26v1.23h-4.34v-1.25c.01-3.76,3.06-6.8,6.82-6.8h-.02.01Z"/>
          <path id="Path_6097" data-name="Path 6097" class="cls-6" d="M121.24,74.53h-9.64l-1.88,5h-2.24l7.78-20.92h2.43l7.7,20.92h-2.24l-1.91-5ZM116.48,61.31l-4.19,11.45h8.37l-4.18-11.45Z"/>
          <path id="Path_6097_-_Outline" data-name="Path 6097 - Outline" class="cls-9" d="M127.18,80.81h-4.9l-1.9-5h-7.9l-1.88,5h-4.91l8.72-23.42h4.17l8.6,23.42ZM114.06,71.5h4.8l-2.4-6.56-2.4,6.56Z"/>
          <path id="Path_6096" data-name="Path 6096" class="cls-8" d="M68.48,119.57c-.06.39-.16.78-.3,1.15,4.16.24,7.4,3.72,7.33,7.89,0,4.85-4.12,7.79-11.7,8.61-.22-.64-.5-1.25-.85-1.83,6.31-.6,10.31-2.79,10.31-6.73v-.24c-.06-3.15-2.61-5.7-5.76-5.76-1.29,3.41-3.36,6.47-6.06,8.92.19.58.45,1.13.79,1.64l-1.83,1.15c-.33-.46-.57-.98-.73-1.52-1.62,1.37-3.64,2.18-5.76,2.29-2.02.03-3.69-1.58-3.72-3.6,0-.15,0-.31.02-.46.49-4.41,3.61-8.08,7.89-9.28-.06-1.58-.06-3.33-.06-5.09-5.16,0-5.7,0-6.67-.06l-.06-2c1.58.06,4.19.06,6.79.06,0-1.37.06-3.09.12-4.55l3,.24c-.11.35-.43.58-.79.6-.06,1.09-.12,2.55-.24,3.64,4.09-.08,8.17-.5,12.19-1.27l.24,2.06c-4.14.74-8.34,1.15-12.55,1.21-.06,1.58-.12,3.09-.06,4.55,1.47-.46,3.01-.7,4.55-.73h1.22c.13-.45.23-.91.3-1.37l2.39.48h0ZM59.2,130.91c-.54-2.33-.85-4.7-.92-7.09-3.13.98-5.42,3.66-5.88,6.91,0,1.83,1,2.43,2.12,2.29,1.74-.2,3.38-.94,4.68-2.12h0ZM65.39,122.36c-1.75-.07-3.5.17-5.16.73.03,2.1.26,4.19.67,6.25,2-1.97,3.53-4.36,4.49-7v.02Z"/>
          <path id="Path_6096_-_Outline" data-name="Path 6096 - Outline" class="cls-10" d="M57.01,108.81l5.89.47-.47,1.5c-.14.43-.41.8-.77,1.07,0,.49-.05,1-.09,1.51,3.56-.14,7.09-.54,10.59-1.21l1.32-.25.54,4.56-1.16.21c-2.51.45-5.05.78-7.6,1l4.64.93-.18,1.1c4.16.95,7.08,4.68,7,8.94,0,3.84-2.22,8.68-12.82,9.84l-1,.11-.32-.95c-.19-.55-.44-1.09-.74-1.59l-.49-.82-1.33.84-.68-1-.22-.33c-1.57.97-3.35,1.53-5.19,1.63h-.12c-2.71,0-4.91-2.2-4.91-4.91,0-.18,0-.36.03-.54.51-4.55,3.55-8.42,7.85-10v-2.94c-4,0-4.58,0-5.5-.06l-1.14-.07-.13-4.48,1.34.05c1.3,0,3.37.06,5.51.06,0-1.18.07-2.42.11-3.35l.04-1.32h0ZM64.81,119.22c0-.11.02-.21.05-.32l.19-1.23c-1.25.1-2.51.17-3.76.2v1.72c1.09-.23,2.19-.35,3.3-.37,0,0,.22,0,.22,0ZM63.13,134.12c3.45-.37,8.88-1.56,8.88-5.46v-.24c-.05-2.16-1.58-3.99-3.7-4.42-1.26,2.96-3.1,5.65-5.4,7.9.11.22.23.43.36.63l.7,1.06-.84.53h0ZM57.81,130.43c-.33-1.56-.56-3.15-.68-4.74-1.87,1.1-3.14,2.99-3.48,5.13,0,1,.36,1,.56,1h.16c1.25-.15,2.44-.63,3.45-1.39h-.01Z"/>
        </g>
      </g>
    </g>
  </g>
</svg>