import { Popover } from "antd";
import React, { useState, useCallback } from "react";
import { DataReceivedEvent } from "../../utils/constants";
// import { ReactComponent as ThreeDots } from "./icons/ThreeDots.svg";
import { ReactComponent as HamBurgerMenu } from "./icons/HamBurgerMenu.svg";

import "./styles/ParticipantPopover.scss";

export function ActionPopover({
  localParticipant,
  setRemoteRaisedHands,
  setShowRaiseHand,
  meetingFeatures,
}) {
  const [showPopover, setShowPopover] = useState(false);

  const muteMic = useCallback(async () => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.MUTE_MIC,
      })
    );
    localParticipant.publishData(data, {
      reliable: true,
    });
  }, [localParticipant]);

  const muteCamera = useCallback(async () => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.MUTE_CAMERA,
      })
    );
    localParticipant.publishData(data, {
      reliable: true,
    });
  }, [localParticipant]);

  const stopRaiseHand = useCallback(async () => {
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.STOP_RAISE_HAND_ALL,
      })
    );
    localParticipant.publishData(data, {
      reliable: true,
    });
    setRemoteRaisedHands(new Map());
    setShowRaiseHand(false);
  }, [localParticipant]);

  const content = (
    <div className="pp-menu">
      {meetingFeatures?.mute_participant === 1 && (
        <div
          className="pp-menu-item"
          onClick={() => {
            muteMic();
            setShowPopover(false);
          }}
        >
          <div className="pp-menu-inner-text">Mute All</div>
        </div>
      )}
      {meetingFeatures?.mute_participant === 1 && (
        <div
          className="pp-menu-item"
          onClick={() => {
            muteCamera();
            setShowPopover(false);
          }}
        >
          <div className="pp-menu-inner-text">Video off All</div>
        </div>
      )}
      {meetingFeatures?.raise_hand === 1 && (
        <div
          className="pp-menu-item"
          onClick={() => {
            stopRaiseHand();
            setShowPopover(false);
          }}
        >
          <div className="pp-menu-inner-text">Lower Hands All</div>
        </div>
      )}
    </div>
  );

  return (
    <Popover
      content={content}
      title={null}
      trigger="click"
      open={showPopover}
      placement="leftTop"
      overlayInnerStyle={{
        backgroundColor: "#242424",
      }}
      onOpenChange={() => setShowPopover(!showPopover)}
      overlayClassName="pp-menu-outer ppac"
    >
      <div style={{ cursor: "pointer" }} className="p-action-ppo">
        {/* <ThreeDots /> */}
        <HamBurgerMenu />
      </div>
    </Popover>
  );
}
