/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from "react";
import { InputNumber, Modal, Radio, Input, Button, Space } from "antd";
import moment from "moment";
import { isSingleParticipantInBreakoutRooms, parseMetadata } from "../../utils/helper";
import { BreakoutRoomService } from "../../services/BreakoutRoomServices";

import "./styles/BreakoutRoomModal.scss";
import { DataReceivedEvent, DrawerState } from "../../utils/constants";

export function BreakoutRoomModal({
  isBreakoutRoomModalOpen,
  setIsBreakoutRoomModalOpen,
  breakoutRooms,
  setDrawerState,
  remoteParticipantsArray,
  setBreakoutRoomDuration,
  meetingDetails,
  setBreakoutRooms,
  setRoomKeyCounter,
  setIsBreakoutRoomCnfigSet,
  coHostToken,
  localParticipant,
  apiToken,
  devMode,
}) {
  const [showWarning, setShowWarning] = useState(false);
  const [radioValue, setRadioValue] = useState(1);
  const [timeValue, setTimeValue] = useState(5);
  const [numberOfRooms, setNumberOfRooms] = useState(1);
  const [showRoomWarning, setShowRoomWarning] = useState(false);

  // Check if the user is the only participant in the meeting
  useEffect(() => {
    if (
      isSingleParticipantInBreakoutRooms(breakoutRooms, remoteParticipantsArray)
    ) {
      setShowWarning(true);
    } else {
      setShowWarning(false);
    }
  }, [breakoutRooms, remoteParticipantsArray]);

  // Function to handle cancel of modal
  const handleCancel = () => {
    setIsBreakoutRoomModalOpen(false);
  };

  // Function to handle number of rooms change
  const onNumberOfRoomsChange = (value) => {
    if (value <= 0 || value === null) {
      setShowRoomWarning(true);
      setNumberOfRooms(0);
      return;
    }
    setShowRoomWarning(false);
    setNumberOfRooms(value);
  };

  // Function to handle radio change
  const onChange = (e) => {
    setRadioValue(e.target.value);
  };

  // Function to handle time change
  const handleTimeChange = (operation) => {
    const newValue = operation === 1 ? timeValue + 5 : timeValue - 5;

    // Assuming meetdetails is available in the scope
    const endDate = moment(meetingDetails?.end_date);
    const currentDate = moment();
    const maxDuration = (endDate - currentDate) / (1000 * 60); // Convert milliseconds to minutes

    if (newValue >= 5 && newValue <= 30 && newValue <= maxDuration) {
      setTimeValue(newValue);
      setBreakoutRoomDuration(newValue);
    }
  };

  // Check if the time value is greater than the remaining time
  useEffect(() => {
    const endDate = moment(meetingDetails?.end_date);
    const intervalId = setInterval(() => {
      const currentDate = moment();
      const maxDuration = (endDate - currentDate) / (1000 * 60); // Convert milliseconds to minutes
      if (timeValue > maxDuration) {
        setTimeValue(timeValue - 5);
        setBreakoutRoomDuration(timeValue - 5);
      }
    }, 10000); // Check every 10 seconds

    return () => clearInterval(intervalId);
  }, []);

  // Function to auto assign breakout rooms
  const autoAssignRooms = () => {
    const filteredParticipants = remoteParticipantsArray.filter(
      (participant) =>
        !["moderator", "cohost"].includes(
          parseMetadata(participant.metadata)?.role_name
        )
    );

    if (filteredParticipants.length === 0) return;

    const totalParticipants = filteredParticipants.length;
    const totalParticipantsPerRoom = Math.floor(
      totalParticipants / numberOfRooms
    );
    const remainingParticipants = totalParticipants % numberOfRooms;
    const roomAssignments = {};

    filteredParticipants.forEach((participant, index) => {
      let roomKey;
      if (index < totalParticipants - remainingParticipants) {
        // First assign participants evenly
        roomKey = Math.floor(index / totalParticipantsPerRoom) + 1;
      } else {
        // Assign remaining participants
        roomKey = index - (totalParticipants - remainingParticipants) + 1;
      }
      if (roomKey > numberOfRooms) roomKey = numberOfRooms;

      if (roomAssignments[roomKey]) {
        roomAssignments[roomKey].push(participant.identity);
      } else {
        roomAssignments[roomKey] = [participant.identity];
      }
    });

    Object?.keys(roomAssignments).forEach(async (key) => {
      if (roomAssignments[key].length === 0) return;
      const response = await BreakoutRoomService.moveParticipantToBreakoutRoom(
        roomAssignments[key],
        meetingDetails?.room_uid,
        `${meetingDetails?.room_uid}__BR${key}`,
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
      if (response?.success === 0) {
        console.log("Error in moving participant to breakout room");
        return;
      }
      // console.log("Participant moved to breakout room", response);
    });
    const updatedRooms = { ...breakoutRooms };

    for (let i = 1; i <= numberOfRooms; i += 1) {
      setBreakoutRooms((rooms) => {
        if (rooms[i]) {
          rooms[i].participants =
            filteredParticipants.filter((p) => {
              if (roomAssignments[i] && roomAssignments[i].length > 0) {
                return roomAssignments[i].includes(p.identity);
              }
            }) || [];
        } else {
          rooms[i] = {
            name: `Breakout Room ${i}`,
            participants:
              filteredParticipants.filter((p) => {
                if (roomAssignments[i] && roomAssignments[i].length > 0) {
                  return roomAssignments[i].includes(p.identity);
                }
              }) || [],
            key: i.toString(),
            manual: true,
            isDeleted: false,
          };
        }
        if (!rooms["0"]) {
          rooms["0"] = {
            name: "Main Room",
            participants: remoteParticipantsArray.filter((p) =>
              ["moderator", "cohost"].includes(parseMetadata(p.metadata).role_name)
            ),
            key: "0",
          };
          updatedRooms["0"] = rooms["0"];
        }
        updatedRooms[i] = rooms[i];
        return { ...rooms };
      });
    }
    return updatedRooms;
  };

  // Function to create breakout rooms
  const handleCreate = async () => {
    if (isSingleParticipantInBreakoutRooms(breakoutRooms, remoteParticipantsArray)) {
      setShowWarning(true);
      return;
    }
    if (numberOfRooms === 0) {
      setShowRoomWarning(true);
      return;
    }
    setShowRoomWarning(false);
    setShowWarning(false);
    let addedRooms = { ...breakoutRooms };
    if (radioValue === 1) {
      for (let i = 1; i <= numberOfRooms; i += 1) {
        let rkc = String(i);
        addedRooms[rkc] = {
          name: `Breakout Room ${i}`,
          manual: true,
          participants: [],
          key: rkc,
          isDeleted: false,
        };
      }
      if(!addedRooms["0"]) {
        addedRooms["0"] = {
          name: "Main Room",
          participants: remoteParticipantsArray,
          key: "0",
        };
      }
      setBreakoutRooms(addedRooms);
      setRoomKeyCounter(numberOfRooms + 1);
    } else if (radioValue === 2) {
      await autoAssignRooms();
    }
    const breakoutRoomIds = [];
    for (let i = 1; i <= numberOfRooms; i += 1) {
      breakoutRoomIds.push(`${meetingDetails?.room_uid}__BR${i}`);
    }
    try {
      await BreakoutRoomService.saveBreakoutRoomConfig(
        meetingDetails?.room_uid,
        breakoutRoomIds,
        radioValue === 1 ? "manual" : "auto",
        timeValue.toString(),
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
    } catch (error) {
      console.error("Error creating breakout room", error);
    }
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.BREAKOUT_ROOM_UPDATE,
        breakoutRooms: addedRooms,
      })
    );
    localParticipant.publishData(data, {
      reliable: true,
    });
    setIsBreakoutRoomCnfigSet(true);
    setDrawerState(DrawerState.BREAKOUTROOM);
  };

  return (
    <Modal
      title="Create Breakout Room"
      open={isBreakoutRoomModalOpen}
      onCancel={handleCancel}
      footer={null}
      className="br-modal"
    >
      {/* <div> */}
      <div className="br-modal-top">
        <div className="br-number">
          <span>Number</span>
          <InputNumber
            defaultValue={1}
            onChange={onNumberOfRoomsChange}
            value={numberOfRooms}
          />
          <span>of breakout rooms</span>
        </div>
        {showRoomWarning && (
          <div
            style={{
              marginTop: "5px",
              color: "red",
              textAlign: "center",
              fontSize: "14px",
            }}
          >
            Room number should be greater than 0
          </div>
        )}
        {/* <span className="br-modal-top-subh">{`${0} participants per room`}</span> */}
      </div>
      <div className="br-radio">
        <Radio.Group onChange={onChange} value={radioValue}>
          <Space direction="vertical">
            <Radio value={1}>Assign Manually</Radio>
            <Radio value={2}>Assign Automatically</Radio>
            {/* <Radio value={3}>Allow participants choose room</Radio> */}
          </Space>
        </Radio.Group>
      </div>
      <div className="br-time">
        <span className="br-time-text">Time</span>
        <div className="br-time-input">
          <Input readOnly value={`${timeValue} min`} />
          <div className="br-time-input-buttons">
            <Button type="primary" onClick={() => handleTimeChange(0)}>
              -
            </Button>
            <Button type="primary" onClick={() => handleTimeChange(1)}>
              +
            </Button>
          </div>
        </div>
        <span className="br-time-text">for breakout room</span>
      </div>
      {showWarning && (
        <div
          style={{
            color: "red",
            textAlign: "center",
            fontSize: "14px",
          }}
        >
          You are only participant in the meeting
        </div>
      )}
      <div className="br-modal-cta">
        <Button type="primary" onClick={handleCancel}>
          CANCEL
        </Button>
        <Button
          type="primary"
          onClick={() => handleCreate()}
          style={{
            cursor: showWarning || showRoomWarning ? "not-allowed" : "pointer",
          }}
        >
          CREATE
        </Button>
      </div>
      {/* </div> */}
    </Modal>
  );
}
