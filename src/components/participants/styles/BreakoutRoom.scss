@use "../../../styles/variables" as *;

.br-user-info {
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 8px;
    font-weight: 400;
    .ant-avatar {
      object-fit: cover;
      width: 35px;
      height: 35px;
      border-radius: 50%;
    }
    .user-details {
      display: flex;
      flex-direction: column;
      .username {
        color: #fff;
        font: 12px Segoe UI, sans-serif;
      }
      .timestamp {
        color: #818088;
        margin-top: 5px;
        font: 10px Segoe UI, sans-serif;
      }
    }
    .user-options {
      width: auto !important;
    }
  }
  .srp {
    width: auto;
    .ant-popover-content{
      width: auto;
    }
    .ant-popover-arrow {
      display: none;
    }
    .ant-popover-inner{
      background-color: #242424;
      border-radius: 10px;
      width: auto;
      .ant-popover-title{
        color: white;
        padding-left: 0.5rem;
        width: auto;
      }
      .ant-popover-inner-content{
        width: auto;
        padding: 0;
        padding-bottom: 1rem;
        .ant-list-item{
          padding-left: 1.5rem;
          border: none;
          color: #D9D9D9;
          &:hover{
            background-color: #3c3c3c;
          }
        }
      }
    }
  }
  .br-create-room{
    display: flex;
    justify-content: center;
    margin-bottom: 0.5rem;

    .ant-btn{
      display: flex;
      align-items: center;
      span{
        font-family: $font;
      }
    }
}