@use "../../../styles/variables" as *;

.post-card {
  background-color: #000;
  display: flex;
  max-width: 100%;
  gap: 20px;
  justify-content: space-between;
  padding: 2px 16px;
  padding-right: 8px;
}

.user-info {
  display: flex;
  gap: 8px;
  font-weight: 400;
  width: 100%;
  .ant-avatar {
    object-fit: cover;
    width: 38px;
    height: 34px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .user-details {
    display: flex;
    flex-direction: column;
    width: 100%;
    .ptc-icos{
      display: flex;
      justify-content: space-between;
    }
    .username {
      color: #fff;
      font: 12px Segoe UI, sans-serif;
      text-align: left;
    }
    .timestamp {
      color: #818088;
      margin-top: 5px;
      font: 10px Segoe UI, sans-serif;
    }
  }
}
.action-icons {
  display: flex;
  gap: 20px;
  padding: 2px 0;
  margin-right: 8px;
  .icon {
    object-fit: contain;
    display: flex;
    align-items: center;
    height: auto;
    .ppo-icon{
      padding: 0 4px;
    }
    svg{
      width: 20px;
      height: auto;
    }
    &:nth-child(3){
      svg{
        height: 16px !important;
      }
    }
  }
}
.action-icons-admin{
  margin: 0;
}


.icon-1 {
  aspect-ratio: 0.83;
  width: 100%;
  flex: 1;
}
.icon-2 {
  aspect-ratio: 0.93;
  width: 28px;
  font-size: 20px;
}
.icon-3 {
  aspect-ratio: 0.33;
  width: 7px;
  margin: auto 0;
}
@media (max-width: 768px) {
  .post-card {
    gap: 10px;
  }
  .action-icons {
    justify-content: flex-end;
  }
}
// WaitingParticipantCard.js
.post-card-waiting{
  display: flex;
  flex-direction: column;
  padding: 13px 16px;
  .wpc-buttons{
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    button{
      padding: 0 6px;
    }
    button:nth-child(1){
      color: white;
      border: 1px solid #fff;
    }
    button:nth-child(2){
      color: #0A84FF;
      border: 1px solid #0A84FF;
    }
  }
}
.wpc-user-details{
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.username{
  color: white;
}