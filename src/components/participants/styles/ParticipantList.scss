$border-style: 2px solid #616161;

@mixin meeting-box{
  overflow-y: auto;
  ::-webkit-scrollbar {
    width: 0px;
  }
  .ant-collapse{
    @include collapse;
  }
  .ant-collapse-header {
    border: none;
  }
  .ant-collapse-item {
    border-radius: 6px;
    border: none;
    .ant-collapse-header {
      color: white;
      flex-direction: row-reverse;
    }
  }
  .ant-collapse-item-active .ant-collapse-header {
    padding: 5px;
  }
  .ant-collapse-content {
    background-color: transparent;
  }
  .ant-collapse-content-box {
    padding: 0;
  }
}

@mixin collapse {
  background-color: transparent;
  border-radius: 6px;
  // border: 2px solid #616161;
}

.pt-container-below {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  // overflow: hidden;
  // height: 100%;
  height: 95%;
  .ant-list{
    // height: 80%;
    height: 95%;
    overflow-y: auto;
  }
  .pt-search {
    width: 100% !important;
    .ant-input-affix-wrapper {
      border-radius: 6px 0 0 6px !important;
      padding-bottom: 5px;
      background-color: #242424 !important;
      border: none;
      .ant-input {
        background-color: transparent;
        color: white;
      }
      .anticon-close-circle {
        color: white;
      }
    }
    .ant-input-group-addon {
      border-radius: 0 6px 6px 0 !important;
      background-color: #242424 !important;
    }
    .ant-input-search-button {
      border-radius: 0 6px 6px 0 !important;
      background-color: transparent !important;
      color: white !important;
      border: none;
      height: 1rem;
    }
  }
  .pt-collapse {
    border: none !important;
    .ant-collapse{
      display: flex;
      flex-direction: column;
      gap: 1rem;
      border: none;
      .ant-collapse-item{
        border: $border-style;
      }
      .ant-collapse-item-active{
        border: $border-style;
        .ant-collapse-header{
          border-bottom: #616161
        }
      }
    }
    @include meeting-box;
    .waiting-in-lobby-buttons{
      display: flex;
      justify-content: flex-end;
      margin-top: 1rem;
      gap: 2rem;
      button{
        padding: 0;
      }
      button:nth-child(1){
        color: white;
        :active{
          color: #c2c2c2;
        }
      }
      button:nth-child(2){
        color: #0A84FF;
        margin-right: 1rem;
        :active{
          color: #005fbf
        }
      }
    }
  }
  .pt-in-meeting{
    @include meeting-box;
  }
}
.pt-mic-off-ico {
  width: 100%;
}

.sd-container-below{
  .pt-container-below{
    .pt-collapse{
      .ant-collapse{
        .ant-collapse-header{
          display: flex;
          align-items: center;
          .ant-collapse-expand-icon{
            display: flex;
          }
        }
        .wil{
          .waiting-in-lobby-buttons{
            margin-bottom: 1rem;
          }
          .ant-list-items{
            max-height: 25vh !important;
          }
        }
      }
    }
    .inm{
      border-radius: 6px !important;
      .ant-list-items{
        max-height: 40vh;
        height: auto;
        overflow-x: hidden;
      }
    }
  }
}

.in-meeting-participants{
  .ant-list-items{
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    .post-card{
      &:nth-child(1){
        .ptc-icos{
          .action-icons-admin{
            margin-right: 5px;
          }
        }
      }
    }
  }
}