// Variables for common styles
$modal-background-color: #242424;
$modal-text-color: white;
$modal-header-text-color: #d9d9d9;
$font-size: 1.2rem;
$font-weight: 700;
$input-width: 60px;
$input-border-radius: 7px;
$input-background-color: #242424;
$input-text-color: white;

// Mixin for shared input styles
@mixin input-styles {
  width: $input-width;
  border-radius: $input-border-radius;
  color: $input-text-color;
  background-color: $input-background-color;
}

@mixin bold-modal-text{
  font-size: $font-size;
  font-weight: $font-weight;
  gap: 8px;
  display: flex;
  align-items: center;
}

.br-modal {
  .ant-modal-content {
    border-radius: 12px;
    background-color: $modal-background-color;
    color: $modal-text-color;
    width: fit-content;
    height: fit-content;
  }

  .ant-modal-header {
    border-radius: 12px;
    background-color: $modal-background-color;
    border-bottom: none;

    .ant-modal-title {
      color: $modal-header-text-color;
      font-weight: 600;
      font-size: $font-size;
      font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto",
        "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
        "Helvetica Neue", sans-serif;
    }

    
  }
  .ant-modal-close-x {
    color: $modal-text-color;
  }

  .ant-modal-body {
    padding: 8px 20px 10px 20px;
  }

  &-top{
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    &-subh{
      color:#D9D9D9;
    }
    .br-number {
      @include bold-modal-text;
  
      .ant-input-number {
        @include input-styles;
        font-size: 18px;
        padding: 3px 0;
          .ant-input-number-handler-wrap{
            background-color: transparent;
            opacity: 1 !important;
            .ant-input-number-handler{
              border: none;
              .ant-input-number-handler-up-inner{
                font-size: 10px;
              }
              .ant-input-number-handler-down-inner{
                font-size: 10px;
              }
              svg{
                color: #fff;
              }
            }
        }
      }
      .ant-input-number-focused{
        .ant-input-number-handler-wrap{
          background-color: transparent;
          .ant-input-number-handler{
            border: none;
            svg{
              color: #fff;
            }
          }
        }
      }
    }
  }
  .br-radio{
    padding: 0 20px;
    margin: 15px 0px;
    .ant-radio{
      &:hover{
        .ant-radio-inner{
          border-color: #0A84FF;
        }
      }
      &-inner{
        width: 22.5px;
        height: 22px;
        background-color: transparent;
      }
    }
    .ant-radio-input{
      &:focus{
        border-color: #0A84FF;
      }
      .ant-radio-input{
        border-color: #0A84FF;
      }
    }
    .ant-radio-checked{
      .ant-radio-inner{
        border-color:#0A84FF;
        background-color: transparent;
        &::after{
          background-color: #0A84FF;
          width: 30px;
          height: 30px;
          top: 3px;
          left: 3.2px;
        }
      }
      &::selection{
        background-color: #0A84FF;
      }
    }
  }
  .br-time{
    @include bold-modal-text;
    padding: 10px 20px;
    &-input{
      display: flex;
      input{
        width: 5rem;
        font-size: 18px;
        background-color: transparent;
        border-radius: 8px 0px 0 8px;
        color: white;
        border-right: none;
        &:hover{
          border-color: white;
        }
      }
      &-buttons{
        display: flex;
        flex-direction: column-reverse;
        border: 1px solid white;
        border-radius: 0 8px 8px 0;
        border-left: none;
        
        button{
          padding: 0;
          width: 1.5rem;
          height: auto;
          background-color: transparent;
          border: none;
          // height: 0.8rem;
          line-height: normal;
        }
      }
    }
  }
  &-cta{
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin: 1rem 0;
    margin-right: 20px;
    button{
      border-radius: 8px;
      font-weight: bold;
      font-size: 16px;
      height: 2.3rem;
      width: 6rem;
      display: flex;
      align-items: center;
      justify-content: center;
      &:nth-child(1){
        background-color: transparent;
        border-color: white;
      }
    }
  }

  .ant-space-item {
    label {
      color: $modal-text-color;
    }
  }
}
