.pp-menu-outer{
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  .ant-popover-inner-content{
    padding: 0;
    width: auto;
    height: auto;
  }
  .ant-popover-inner{
    width: auto;
    height: auto;
    border-radius: 10px;
  }
}
.p-action-ppo{
  display: flex;
  align-items: center;
  font-size: 1rem !important;
}
.pp-menu {
  border-radius: 10px;
  padding: 1rem 0;
  box-shadow:  0px 0px 6px #0d0d0d, 0px 0px 1px #393939;
  .pp-menu-item {
    color: white;
    display: flex;
    justify-content: start;
    cursor: pointer;
    // height: 2.5em;
    width: 100%;
    :hover {
      background-color: #000;
      // border-radius: 4px;
    }
    .pp-menu-inner-text {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 100%;
      width: 100%;
      padding: 0.7rem 1rem;
      // padding: 2% 4%;
      color: white;
    }
  }
}
.ppo-icon svg {
  width: 5px !important;
}
@media screen and (max-width: 891px) {
  .pp-menu-inner-text {
    font-size: 1.5vw;
  }
}
@media screen and (max-width: 450px) {
  .pp-menu{
    width: 150px !important;
  }
  .pp-menu-inner-text {
    font-size: 0.8rem;
  }
}

