import { Popover } from "antd";
import React, { useState, useCallback, useEffect } from "react";
import { useTracks } from "@livekit/components-react";
import { Track } from "livekit-client";
import { getLocalStorageToken, parseMetadata } from "../../utils/helper";
import { DataReceivedEvent, DrawerState } from "../../utils/constants";
import { ParticipantService } from "../../services/ParticipantServices";
import { ReactComponent as ThreeDots } from "./icons/ThreeDots.svg";
// import { PrivateMessageModal } from "../PrivateMessageModal";

// import "./ParticipantPopover.scss";
import "./styles/ParticipantPopover.scss";

export function ParticipantsPopOver({
  participant,
  localParticipant,
  meetingUid,
  isHost,
  layoutContext,
  isCoHost,
  coHostToken,
  meetingFeatures,
  forcemute,
  forcevideooff,
  setselectedprivatechatparticipant,
  setprivatechatparticipants,
  privatechatparticipants,
  setDrawerState,
  setshowprivatechat,
  room,
  isBreakoutRoom,
  breakoutRooms,
  allowLiveCollabWhiteBoard,
  numberOfCohostCurrent,
  setNumberOfCohostCurrent,
  isPinned,
  setIsPinned,
  setToastNotification,
  setToastStatus,
  setShowToast,
  apiToken,
  devMode,
}) {
  const cameraTracksReferences = useTracks([Track.Source.Camera]);
  const [showPopover, setShowPopover] = useState(false);

  const removeParticipant = useCallback(async (id, p) => {
    try {
      if (isBreakoutRoom) {
        await ParticipantService.removeParticipant(
          room?.roomInfo?.name,
          p.identity,
          parseMetadata(localParticipant.metadata)?.role_name === "cohost"
            ? coHostToken
            : apiToken,
          localParticipant?.participantInfo,
          devMode
        );
      } else {
        await ParticipantService.removeParticipant(
          id,
          p.identity,
          coHostToken,
          localParticipant?.participantInfo,
          devMode
        );
      }
      setToastNotification("Participant removed");
      setToastStatus("success");
      setShowToast(true);
    } catch (err) {
      setToastNotification(err.message);
      setToastStatus("error");
      setShowToast(true);
      // console.log("Error removing participant:", err);
    }
  }, []);

  const sendPrivateMessage = () => {
    if (privatechatparticipants.length > 0) {
      const participantIndex = privatechatparticipants.findIndex(
        (part) => part.participant.identity === participant.identity
      );
      if (participantIndex !== -1) {
        setselectedprivatechatparticipant(
          privatechatparticipants[participantIndex]
        );
      } else {
        const maxKey = privatechatparticipants.reduce(
          (max, p) => Math.max(max, p.key),
          0
        );
        const newParticipant = {
          key: maxKey + 1,
          participant,
          isConnected: true,
          receivedUnreadMessagesCount: 0,
        };
        // Add the new participant to the list (this may involve setting state)
        setprivatechatparticipants([
          ...privatechatparticipants,
          newParticipant,
        ]);
        setselectedprivatechatparticipant(newParticipant);
      }
    } else {
      const newParticipant = {
        key: 1,
        participant,
        isConnected: true,
        receivedUnreadMessagesCount: 0,
      };
      setprivatechatparticipants([newParticipant]);
      setselectedprivatechatparticipant(newParticipant);
    }
    setshowprivatechat(true);
    setDrawerState(DrawerState.CHAT);
    // dispatch({ msg: "show_chat" });
  };

  const askToUnmuteMic = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.ASK_TO_UNMUTE_MIC,
        })
      );
      localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const muteMic = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.MUTE_MIC,
        })
      );
      localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const askToUnmuteCamera = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.ASK_TO_UNMUTE_CAMERA,
        })
      );
      localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const muteCamera = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.MUTE_CAMERA,
        })
      );
      localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const pinToScreen = useCallback(() => {
    try {
      if (!isPinned) {
        const participantTrackRef = cameraTracksReferences.find(
          (track) => track.participant.identity === participant.identity
        );
        if (!participantTrackRef) {
          setToastNotification("Cannot pin participant to screen");
          setToastStatus("error");
          setShowToast(true);
          return;
        }
        setIsPinned(true);
        layoutContext.pin.dispatch?.({
          msg: "set_pin",
          trackReference: participantTrackRef,
        });
        setToastNotification(`${participant.name} pinned to screen`);
        setToastStatus("success");
        setShowToast(true);
      } else {
        layoutContext.pin.dispatch?.({
          msg: "clear_pin",
        });
        setIsPinned(false);
        setToastNotification(`${participant.name} unpinned from screen`);
        setToastStatus("success");
        setShowToast(true);
      }
    } catch (error) {
      setToastNotification("Failed to pin participant to screen");
      setToastStatus("error");
      setShowToast(true);
    }
  }, [
    participant,
    layoutContext,
    isPinned,
    localParticipant,
    cameraTracksReferences,
    setIsPinned,
  ]);

  const makeCohost = useCallback(async () => {
    try {
      const participantAlreadyCohost =
        parseMetadata(participant.metadata)?.role_name === "cohost";
      const response = await ParticipantService.makeCoHost(
        meetingUid,
        participant,
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
      if (response.success === 0) {
        setToastNotification("Error making co-host");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      const encoder = new TextEncoder();
      let data;
      if (participantAlreadyCohost) {
        data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.REMOVE_CO_HOST,
          })
        );
        setNumberOfCohostCurrent((prev) => prev - 1);
        setToastNotification(`${participant.name} removed as co-host`);
      } else {
        data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.MAKE_CO_HOST,
            token: isHost ? apiToken : coHostToken,
            forceMute: forcemute,
            forceVideoOff: forcevideooff,
            breakoutRooms,
            allowLiveCollabWhiteBoard,
          })
        );
        setNumberOfCohostCurrent((prev) => prev + 1);
        setToastNotification(`${participant.name} made co-host`);
      }
      localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [participant.identity],
      });
      setToastStatus("success");
      setShowToast(true);
    } catch (error) {
      setToastNotification("Error making co-host");
      setToastStatus("error");
      setShowToast(true);
    }
  }, [localParticipant, meetingUid, participant]);

  const content = (
    <div className="pp-menu">
      {!participant.isMicrophoneEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              askToUnmuteMic(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Ask To Unmute Mic</div>
          </div>
        )}

      {participant.isMicrophoneEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              muteMic(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Mute Mic</div>
          </div>
        )}
      {!participant.isCameraEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              askToUnmuteCamera(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Ask To TurnON Camera</div>
          </div>
        )}

      {participant.isCameraEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              muteCamera(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Turn Off Camera</div>
          </div>
        )}
      {(isHost ||
        (isCoHost &&
          parseMetadata(participant.metadata)?.role_name !== "moderator")) &&
        (parseMetadata(participant.metadata)?.role_name === "cohost" || // Always allow "Remove Co-Host"
          meetingFeatures?.configurations?.allow_multiple_cohost === 1 || // If multi-cohost is allowed, show "Make Co-Host"
          numberOfCohostCurrent < 1) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              makeCohost();
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">
              {parseMetadata(participant.metadata)?.role_name === "cohost"
                ? "Remove Co-Host"
                : "Make Co-Host"}
            </div>
          </div>
        )}
      <div className="pp-menu-item" onClick={() => pinToScreen()}>
        <div className="pp-menu-inner-text">
          {isPinned ? "Remove Pin" : "Pin To Screen"}
        </div>
      </div>
      {(isHost ||
        (isCoHost &&
          parseMetadata(participant.metadata)?.role_name !== "moderator")) && (
        <div
          className="pp-menu-item"
          onClick={() => {
            removeParticipant(meetingUid, participant);
            setShowPopover(false);
          }}
        >
          <div className="pp-menu-inner-text">Remove From Call</div>
        </div>
      )}
      {meetingFeatures?.configurations?.enable_private_chat === 1 && (
        <div className="pp-menu-item" onClick={() => sendPrivateMessage()}>
          <div className="pp-menu-inner-text">Send Private Message</div>
        </div>
      )}
    </div>
  );

  return (
    <Popover
      content={content}
      title={null}
      trigger="click"
      open={showPopover}
      placement="leftTop"
      overlayInnerStyle={{
        backgroundColor: "#1c1c1e",
      }}
      onOpenChange={() => setShowPopover(!showPopover)}
      overlayClassName="pp-menu-outer"
    >
      <div style={{ cursor: "pointer" }} className="ppo-icon-participant">
        <ThreeDots />
      </div>
    </Popover>
  );
}
