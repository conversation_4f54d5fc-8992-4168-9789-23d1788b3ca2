import React from "react";
import { Avatar } from "antd";
import { generateAvatar, parseMetadata } from "../../utils/helper";
// import "./BreakoutRoom.scss";
import "./styles/BreakoutRoom.scss";

export function BRParticipantCard({ participant }) {
  const metadata = parseMetadata(participant.metadata);
  const role = `${
    metadata?.role_name === "moderator"
      ? "(Host)"
      : metadata?.role_name === "cohost"
      ? "(Co-Host)"
      : ""
  }`;
  
  return (
    <div title={participant.name} className="br-user-info">
      <Avatar
        style={{
          backgroundColor: "#fd4563",
          verticalAlign: "middle",
        }}
      >
        {generateAvatar(participant.name)}
      </Avatar>
      <div className="user-details">
        <div className="username">
          {participant.name}
          {/* {participant.isLocal ? " (You)" : ""} */}
        </div>
        <div className="timestamp">{role}</div>
      </div>
    </div>
  );
}
