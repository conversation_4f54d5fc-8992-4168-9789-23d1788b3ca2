import React from "react";

import { DrawerState } from "../../utils/constants";
import "../../styles/ControlBar.scss";
import "../../styles/Settings.scss";
import "../../styles/index.scss";
import { ReactComponent as SvgParticipantIcon } from "./icons/ParticipantIcon.svg";
import { ReactComponent as SvgParticipantBlueIcon } from "./icons/ParticpantOnIcon.svg";

export function ParticipantControlButton({
  showParticipantsList,
  setShowParticipantsList,
  setDrawerState,
}) {
  return (
    <div
      onClick={() => {
        setShowParticipantsList(!showParticipantsList);
        setDrawerState(DrawerState.PARTICIPANTS);
      }}
      className="lk-button control-bar-button control-bar-button-icon participants-icon"
    >
      {showParticipantsList ? (
        <SvgParticipantBlueIcon />
      ) : (
        <SvgParticipantIcon />
      )}
    </div>
  );
}
