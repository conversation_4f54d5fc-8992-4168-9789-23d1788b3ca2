import React, { useEffect, useState } from "react";
import { Button, Checkbox, Collapse, List, Popover, Tooltip } from "antd";
import { isSingleParticipantInBreakoutRooms } from "../../utils/helper";
import SideDrawer from "../SideDrawer";
import "../../styles/BreakoutRoomDrawer.scss";
import { BRParticipantCard } from "./BRParticipantCard";
import { BreakoutRoomCard } from "./BreakoutRoomCard";
import { BreakoutRoomService } from "../../services/BreakoutRoomServices";
import { ReactComponent as DeleteBreakoutRoom } from "./icons/DeleteBreakoutRoom.svg";
import { ReactComponent as AddParticipant } from "./icons/AddParticipant.svg";
import { DataReceivedEvent, DrawerState } from "../../utils/constants";

const { Panel } = Collapse;

export default function BreakoutRoomDrawer({
  showBreakoutRoom,
  setShowBreakoutRoom,
  breakoutRooms,
  setBreakoutRooms,
  id,
  roomKeyCounter,
  setRoomKeyCounter,
  meetingDetails,
  breakoutRoomDuration,
  coHostToken,
  isHost,
  setDrawerState,
  setIsBreakoutRoomCnfigSet,
  localParticipant,
  isWhiteboardOpen,
  setToastNotification,
  setToastStatus,
  setShowToast,
  apiToken,
  devMode,
}) {
  const [mainRoomParticipants, setMainRoomParticipants] = useState([]);
  const [activeKey, setActiveKey] = useState(["0"]);
  const [participantMenuVisible, setParticipantMenuVisible] = useState(null);
  const [selectedParticipants, setSelectedParticipants] = useState({});
  const [showWarning, setShowWarning] = useState(false);
  const [assignParticipantTooltipVisible, setAssignParticipantTooltipVisible] =
    useState({});

  const handleTooltipVisibility = (key, visible) => {
    setAssignParticipantTooltipVisible((prev) => ({
      ...prev,
      [key]: visible,
    }));
  };

  useEffect(() => {
    // Ensure breakoutRooms is defined and an object
    if (typeof breakoutRooms !== "object") return;

    if (isSingleParticipantInBreakoutRooms(breakoutRooms, mainRoomParticipants))
      setShowWarning(true);
    else setShowWarning(false);

    // Find the breakout room with key 0 and update participants if necessary
    const mainRoom = breakoutRooms["0"];
    if (mainRoom) {
      setMainRoomParticipants(mainRoom.participants);
    }

    Object?.entries(breakoutRooms).forEach(([key, value]) => {
      if (key !== "0") {
        setSelectedParticipants((prevSelectedParticipants) => {
          const updatedSelectedParticipants = {};
          updatedSelectedParticipants[key] = [
            ...(prevSelectedParticipants[key] || []),
            ...value.participants,
          ];
          return {
            ...prevSelectedParticipants,
            ...updatedSelectedParticipants,
          };
        });
      }
    });
  }, [breakoutRooms]);

  // function to add breakout room
  const handleAddBreakoutRoom = async () => {
    let rkc = String(roomKeyCounter);
    const newBreakoutRooms = { ...breakoutRooms };
    newBreakoutRooms[rkc] = {
      name: `Breakout Room ${roomKeyCounter}`,
      manual: true,
      participants: [],
      key: rkc,
      isDeleted: false,
    };
    setBreakoutRooms(newBreakoutRooms);
    try {
      // Save the breakout room configuration
      await BreakoutRoomService.saveBreakoutRoomConfig(
        meetingDetails?.room_uid,
        [`${meetingDetails?.room_uid}__BR${rkc}`],
        "manual",
        breakoutRoomDuration.toString(),
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
    } catch (error) {
      setToastNotification("Error creating breakout room");
      setToastStatus("error");
      setShowToast(true);
    }
    setRoomKeyCounter((prev) => prev + 1);
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.BREAKOUT_ROOM_UPDATE,
        breakoutRooms: newBreakoutRooms,
      })
    );
    localParticipant.publishData(data, {
      reliable: true,
    });
  };

  // const handleEdit = (roomKey, roomName) => {
  //   setEditingRoomKey(roomKey);
  //   setNewRoomName(roomName);
  // };

  // const handleSave = (roomKey) => {
  //   setBreakoutRooms((rooms) => {
  //     rooms[roomKey] = { ...rooms[roomKey], name: newRoomName };
  //     return { ...rooms };
  //   });
  //   setEditingRoomKey(null);
  //   setNewRoomName("");
  // };

  const handleDelete = async (roomKey) => {
    const confirmDelete = window.confirm(
      "Are you sure you want to delete this room?"
    );
    if (!confirmDelete) return;

    // Move participants to main room before deleting the breakout room
    const roomToDelete = breakoutRooms[roomKey];
    if (roomToDelete && roomToDelete.participants) {
      if (roomToDelete.participants.length !== 0) {
        try {
          const participantIds = roomToDelete.participants.map(
            (p) => p?.identity
          );
          const response =
            await BreakoutRoomService.moveParticipantToBreakoutRoom(
              participantIds,
              `${id}__BR${roomKey}`,
              id,
              isHost ? apiToken : coHostToken,
              localParticipant?.participantInfo,
              devMode
            );
          if (response?.success === 0) {
            console.log(
              "Error in moving participant to main room",
              response?.message
            );
            setToastNotification("Error in moving participant to main room");
            setToastStatus("error");
            setShowToast(true);
            return;
          }
          // console.log("Participant moved to main room", response);
        } catch (error) {
          console.log("Error in moving participant to main room", error);
          setToastNotification("Error in moving participant to main room");
          setToastStatus("error");
          setShowToast(true);
          return;
        }
        // Delete the selected participants of that roomkey and move them to main room
        setSelectedParticipants((prevSelectedParticipants) => {
          const updated = { ...prevSelectedParticipants };
          delete updated[roomKey];
          return updated;
        });
      }
      // update the isDeleted flag to true for the room to be deleted and if not present then add and set false
      const prevRooms = { ...breakoutRooms };
      if (prevRooms[roomKey]) {
        prevRooms[roomKey].isDeleted = true;
        prevRooms[roomKey].manual = false;
      }
      setBreakoutRooms(prevRooms);
      const activeRooms = Object.keys(prevRooms).filter(
        (key) => !prevRooms[key].isDeleted || !("isDeleted" in prevRooms[key])
      );
      if (activeRooms.length <= 1) {
        setDrawerState(DrawerState.PARTICIPANTS);
        setIsBreakoutRoomCnfigSet(false);
      }
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.BREAKOUT_ROOM_UPDATE,
          breakoutRooms: prevRooms,
        })
      );
      localParticipant.publishData(data, {
        reliable: true,
      });
    }
  };

  const onChange = (key) => {
    setActiveKey([key]);
  };

  const handleSelectedParticipants = (brRoom, checked, participant) => {
    if (isSingleParticipantInBreakoutRooms(breakoutRooms, mainRoomParticipants))
      setShowWarning(true);
    else setShowWarning(false);

    setSelectedParticipants((prev) => {
      const updated = { ...prev };
      if (checked) {
        updated[brRoom.key] = [...(updated[brRoom.key] || []), participant];
      } else {
        updated[brRoom.key] = updated[brRoom.key].filter(
          (p) => p.identity !== participant.identity
        );
      }
      return updated;
    });
  };

  const updateBreakoutRoomParticipants = async (roomKey) => {
    try {
      if (
        isSingleParticipantInBreakoutRooms(breakoutRooms, mainRoomParticipants)
      )
        return;
      const particpantsIds = selectedParticipants[roomKey].map(
        (p) => p.identity
      );
      if (particpantsIds.length === 0) return;
      const response = await BreakoutRoomService.moveParticipantToBreakoutRoom(
        particpantsIds,
        id,
        `${id}__BR${roomKey}`,
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
      if (response?.success === 0) {
        setToastNotification("Error in moving participant to breakout room");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      // console.log("Participant moved to breakout room", response);
    } catch (error) {
      setToastNotification("Error in moving participant to breakout room");
      setToastStatus("error");
      setShowToast(true);
      return;
    }
    setBreakoutRooms((rooms) => {
      if (rooms[roomKey]) {
        rooms[roomKey].participants = selectedParticipants[roomKey] || [];
      }
      return { ...rooms };
    });
    setParticipantMenuVisible(null);
  };

  const handleDeleteParticipant = async (participant, roomKey) => {
    try {
      const response = await BreakoutRoomService.moveParticipantToBreakoutRoom(
        [participant?.identity],
        `${id}__BR${roomKey}`,
        id,
        isHost ? apiToken : coHostToken,
        localParticipant?.participantInfo,
        devMode
      );
      if (response?.success === 0) {
        setToastNotification("Error in moving participant to main room");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      // console.log("Participant moved to main room", response);
    } catch (error) {
      setToastNotification("Error in moving participant to main room");
      setToastStatus("error");
      setShowToast(true);
      return;
    }
    setBreakoutRooms((prevRooms) => {
      if (prevRooms[roomKey]) {
        prevRooms[roomKey].participants = prevRooms[
          roomKey
        ].participants.filter((p) => p.identity !== participant.identity);
      }
      if (prevRooms["0"]) {
        prevRooms["0"].participants = [
          ...(prevRooms["0"].participants || []),
          participant,
        ];
      }
      return { ...prevRooms };
    });
    setSelectedParticipants((prevSelectedParticipants) => {
      const updated = { ...prevSelectedParticipants };
      if (updated[roomKey]) {
        updated[roomKey] = updated[roomKey].filter(
          (p) => p.identity !== participant.identity
        );
      }
      return updated;
    });
  };

  const handleSwapParticipant = async (participant, fromRoomKey, toRoomKey) => {
    if (toRoomKey === "0") {
      try {
        const response =
          await BreakoutRoomService.moveParticipantToBreakoutRoom(
            [participant?.identity],
            `${id}__BR${fromRoomKey}`,
            id,
            isHost ? apiToken : coHostToken,
            localParticipant?.participantInfo,
            devMode
          );
        if (response?.success === 0) {
          setToastNotification("Error in moving participant");
          setToastStatus("error");
          setShowToast(true);
          return;
        }
        // console.log("Participant moved to main room", response);
      } catch (error) {
        setToastNotification("Error in moving participant to main room");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      setSelectedParticipants((prevSelectedParticipants) => {
        const updated = { ...prevSelectedParticipants };
        if (updated[fromRoomKey]) {
          updated[fromRoomKey] = updated[fromRoomKey].filter(
            (p) => p.identity !== participant.identity
          );
        }
        return updated;
      });
      setBreakoutRooms((prevRooms) => {
        if (prevRooms[fromRoomKey]) {
          prevRooms[fromRoomKey].participants = prevRooms[
            fromRoomKey
          ].participants.filter((p) => p.identity !== participant.identity);
        }
        if (prevRooms[toRoomKey]) {
          prevRooms[toRoomKey].participants = [
            ...(prevRooms[toRoomKey].participants || []),
            participant,
          ];
        }
        return { ...prevRooms };
      });
    } else {
      try {
        const response =
          await BreakoutRoomService.moveParticipantToBreakoutRoom(
            [participant?.identity],
            `${id}__BR${fromRoomKey}`,
            `${id}__BR${toRoomKey}`,
            isHost ? apiToken : coHostToken,
            localParticipant?.participantInfo,
            devMode
          );
        if (response?.success === 0) {
          setToastNotification("Error in moving participant");
          setToastStatus("error");
          setShowToast(true);
          return;
        }

        // console.log("Participant moved to breakout room", response);
      } catch (error) {
        setToastNotification("Error in moving participant to breakout room");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      setSelectedParticipants((prevSelectedParticipants) => {
        const updated = { ...prevSelectedParticipants };
        if (updated[toRoomKey]) {
          updated[toRoomKey] = [...(updated[toRoomKey] || []), participant];
        }
        if (updated[fromRoomKey]) {
          updated[fromRoomKey] = updated[fromRoomKey].filter(
            (p) => p.identity !== participant.identity
          );
        }
        return updated;
      });
      setBreakoutRooms((prevRooms) => {
        if (prevRooms[fromRoomKey]) {
          prevRooms[fromRoomKey].participants = prevRooms[
            fromRoomKey
          ].participants.filter((p) => p.identity !== participant.identity);
        }
        if (prevRooms[toRoomKey]) {
          prevRooms[toRoomKey].participants = [
            ...(prevRooms[toRoomKey].participants || []),
            participant,
          ];
        }
        return { ...prevRooms };
      });
    }
  };

  return (
    <SideDrawer
      show={showBreakoutRoom}
      setShow={setShowBreakoutRoom}
      title="Breakout Room"
      isWhiteboardOpen={isWhiteboardOpen}
    >
      <div className="br-body">
        <div className="br-body">
          <div className="br-body-add-room">
            <Button onClick={() => handleAddBreakoutRoom()}>Add Room</Button>
          </div>
          <div className="br-body-rooms">
            {Object.keys(breakoutRooms).length > 0 && (
              <div className="br-body-rooms-room">
                <List
                  dataSource={Object.values(breakoutRooms)}
                  renderItem={(brRoom) =>
                    !brRoom?.isDeleted && (
                      <Collapse
                        activeKey={activeKey}
                        onChange={onChange}
                        accordion
                      >
                        <Panel
                          key={brRoom?.key}
                          header={
                            <div className="br-body-rooms-room-panel-header">
                              {/* {editingRoomKey === brRoom?.key &&
                            brRoom?.key !== "0" ? (
                              <input
                                className="br-room-name"
                                type="text"
                                value={newRoomName}
                                onChange={(e) => {
                                  e.stopPropagation();
                                  setNewRoomName(e.target.value);
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                                onBlur={() => handleSave(brRoom?.key)}
                                ref={inputRef}
                                maxLength={15}
                              /> */}
                              {/* // ) : ( */}
                              <span>{`${brRoom.name} (${brRoom.participants.length})`}</span>
                              {/* // )} */}
                              {brRoom.key !== "0" && (
                                <div className="br-body-rooms-room-panel-header-icons">
                                  {/* {editingRoomKey !== brRoom?.key && (
                                  <img
                                    src={EditBreakoutRoom}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEdit(brRoom?.key, brRoom.name);
                                    }}
                                    alt=""
                                  />
                                )} */}
                                  <Tooltip
                                    placement="left"
                                    color="#1e1e1e"
                                    title="Delete Room"
                                  >
                                    <img
                                      src={DeleteBreakoutRoom}
                                      alt=""
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleDelete(brRoom?.key);
                                      }}
                                    />
                                  </Tooltip>
                                  <Popover
                                    overlayClassName="br-body-rooms-room-panel-header-icons-add-participant"
                                    title={`Assign to ${brRoom.name}`}
                                    placement="left"
                                    content={
                                      <div
                                        onClick={(e) => {
                                          e.stopPropagation();
                                        }}
                                      >
                                        <List
                                          dataSource={mainRoomParticipants}
                                          renderItem={(participant) => (
                                            <List.Item
                                              key={participant?.identity}
                                            >
                                              <Checkbox
                                                className="add-participant-check"
                                                // checked={selectedParticipants[room.key]?.includes(participant.identity) || false}
                                                checked={
                                                  selectedParticipants[
                                                    brRoom?.key
                                                  ]?.some(
                                                    (p) =>
                                                      p?.identity ===
                                                      participant?.identity
                                                  ) || false
                                                }
                                                onChange={(e) => {
                                                  e.stopPropagation();
                                                  handleSelectedParticipants(
                                                    brRoom,
                                                    e.target.checked,
                                                    participant
                                                  );
                                                }}
                                              >
                                                {/* {participant.name} */}
                                                <BRParticipantCard
                                                  participant={participant}
                                                />
                                              </Checkbox>
                                            </List.Item>
                                          )}
                                        />
                                        {showWarning && (
                                          <div
                                            style={{
                                              color: "red",
                                              textAlign: "center",
                                              fontSize: "10px",
                                            }}
                                          >
                                            You are only participant in the
                                            meeting
                                          </div>
                                        )}
                                        <Button
                                          type="primary"
                                          shape="round"
                                          onClick={() =>
                                            updateBreakoutRoomParticipants(
                                              brRoom?.key
                                            )
                                          }
                                        >
                                          Add Participants
                                        </Button>
                                      </div>
                                    }
                                    trigger="click"
                                    open={
                                      participantMenuVisible === brRoom?.key
                                    }
                                    onOpenChange={(visible) =>
                                      setParticipantMenuVisible(
                                        visible ? brRoom?.key : null
                                      )
                                    }
                                  >
                                    {mainRoomParticipants.length > 0 && (
                                      <Tooltip
                                        placement="left"
                                        color="#1e1e1e"
                                        title="Add to Breakout room"
                                        open={
                                          assignParticipantTooltipVisible[
                                            brRoom?.key
                                          ]
                                        }
                                      >
                                        <img
                                          src={AddParticipant}
                                          alt="Add Participant"
                                          onMouseEnter={() => {
                                            handleTooltipVisibility(
                                              brRoom?.key,
                                              true
                                            );
                                          }}
                                          onMouseLeave={() =>
                                            handleTooltipVisibility(
                                              brRoom?.key,
                                              false
                                            )
                                          }
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleTooltipVisibility(
                                              brRoom?.key,
                                              false
                                            );
                                          }}
                                        />
                                      </Tooltip>
                                    )}
                                  </Popover>
                                </div>
                              )}
                            </div>
                          }
                        >
                          <List
                            className="br-room-participants"
                            dataSource={brRoom.participants}
                            renderItem={(participant) => (
                              <BreakoutRoomCard
                                participant={participant}
                                brRoom={brRoom}
                                onDeleteParticipant={handleDeleteParticipant}
                                onSwapParticipant={handleSwapParticipant}
                                breakoutRooms={breakoutRooms}
                              />
                            )}
                          />
                        </Panel>
                      </Collapse>
                    )
                  }
                />
              </div>
            )}
          </div>
          {Object.keys(breakoutRooms).length === 0 && (
            <div className="br-body-suggestion">
              <p>Add a room to get started</p>
            </div>
          )}
        </div>
      </div>
    </SideDrawer>
  );
}
