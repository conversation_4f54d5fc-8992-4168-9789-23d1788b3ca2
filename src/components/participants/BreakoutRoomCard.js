/* eslint-disable no-unused-vars */
import * as React from "react";
import { useState } from "react";
import { List, Popover, Tooltip } from "antd";
// import { stringToColor } from "../../utils/helper";
// import "./BreakoutRoom.scss";
import "./styles/BreakoutRoom.scss";
import { BRParticipantCard } from "./BRParticipantCard";
import { ReactComponent as DeleteUserFromRoom } from "./icons/DeleteBreakoutRoom.svg";
import { ReactComponent as SwapRoom } from "./icons/SwapTo.svg";

export function BreakoutRoomCard({
  participant,
  brRoom,
  onDeleteParticipant,
  breakoutRooms,
  onSwapParticipant,
}) {
  const [visible, setVisible] = useState(false);
  const [swapRoomTooltipVisible, setSwapRoomTooltipVisible] = useState(false);
  const handleClick = (e) => {
    e.stopPropagation(); // Prevent event bubbling
    setSwapRoomTooltipVisible(false);
    setVisible((prev) => !prev);
  };
  const roomsContent = (
    <div>
      <List
        dataSource={Object.values(breakoutRooms)}
        renderItem={(room) =>
          room?.key !== brRoom.key && (
            <List.Item key={room?.key}>
              <div
                onClick={() => {
                  onSwapParticipant(participant, brRoom.key, room.key);
                  setVisible(false);
                }}
              >
                {room.name}
              </div>
            </List.Item>
          )
        }
      />
    </div>
  );

  return (
    <div className="br-user">
      <BRParticipantCard participant={participant} />
      {brRoom?.key !== "0" && (
        <div className="user-options">
          <Popover
            placement="left"
            content={roomsContent}
            title="Switch to"
            trigger="click"
            open={visible}
            onOpenChange={() => setVisible(!visible)}
            overlayClassName="srp"
          >
            <Tooltip
              color="#1e1e1e"
              placement="left"
              title="Swap participant to other room"
              open={swapRoomTooltipVisible}
            >
              <img
                src={SwapRoom}
                alt="Swap Room"
                onMouseEnter={() => setSwapRoomTooltipVisible(true)}
                onMouseLeave={() => setSwapRoomTooltipVisible(false)}
                onClick={handleClick} // Open Popover on click
              />
            </Tooltip>
          </Popover>
          <Tooltip color="#1e1e1e" placement="left" title="Move to Main Room">
            <img
              src={DeleteUserFromRoom}
              alt=""
              onClick={() => onDeleteParticipant(participant, brRoom.key)}
            />
          </Tooltip>
        </div>
      )}
    </div>
  );
}
