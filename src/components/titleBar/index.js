import React, { useEffect } from "react";
import { FaRegWindowMinimize } from "react-icons/fa";
import { FiMaximize } from "react-icons/fi";
import { BsPip } from "react-icons/bs";
import "./titleBar.scss";
import { IoMdClose } from "react-icons/io";
import { But<PERSON>, Modal } from "antd";

const styles = {
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "10px 20px",
    backgroundColor: "#333",
    color: "#fff",
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "2rem",
    zIndex: 1000,
    WebkitUserSelect: "none", // Prevent text selection while dragging
    WebkitAppRegion: "drag",
  },
  title: {
    fontSize: "18px",
    fontWeight: "bold",
    WebkitAppRegion: "drag", // Make this area draggable
  },
  buttons: {
    display: "flex",
    gap: "10px",
  },
  button: {
    background: "transparent",
    border: "none",
    color: "#fff",
    fontSize: "16px",
    cursor: "pointer",
    WebkitAppRegion: "no-drag", // Prevent buttons from being draggable
  },
};

function TitleBar({ pipEnabled = true, title, setIsPipWindow, isPipWindow }) {
  const [windowMaximized, setWindowMaximized] = React.useState(true);
  // const [windowPip, setWindowPip] = React.useState(false);
  const [isCloseMeetingModalOpen, setIsCloseMeetingModalOpen] = React.useState(false);

  const minimizeWindow = () => {
    window?.electronAPI?.ipcRenderer?.send("minimize-window", pipEnabled);
    if (pipEnabled) {
      setIsPipWindow(!isPipWindow);
    }
    // setWindowMaximized(!windowMaximized);
  };

  const completeMinimizeWindow = () => {
    window?.electronAPI?.ipcRenderer?.send("minimize-window", false);
  };

  const closeWindow = () => {
    window?.electronAPI?.ipcRenderer?.send("close-window");
    window?.electronAPI?.ipcRenderer?.send("stop-annotation");
  };

  useEffect(() => {
    // Define the listener function
    const handlePipResponse = (e, data) => {
      setWindowMaximized(!data);
      setIsPipWindow(data);
    };

    // Add the listener
    window?.electronAPI?.ipcRenderer?.on("pip-response", handlePipResponse);

    // Cleanup listener on unmount
    return () => {
      window?.electronAPI?.ipcRenderer?.removeListener(
        "pip-response",
        handlePipResponse
      );
    };
  }, []);

  // const maximizeWindow = () => {
  //   window?.electronAPI?.ipcRenderer?.send("maximize-window", !windowMaximized);
  //   setWindowMaximized(!windowMaximized);
  // };

  // const pipWindow = () => {
  //   if (windowMaximized) {
  //     setWindowMaximized(false);
  //     window?.electronAPI?.ipcRenderer?.send("maximize-window");
  //   }
  //   window?.electronAPI?.ipcRenderer?.send("pip-window");
  //   setWindowPip(!windowPip);
  // };

  return (
    <div style={styles.header} className="title-bar-electron">
      <div style={styles.title} className="title-bar-electron-app-name">
        {title}
      </div>
      <div style={styles.buttons} className="title-bar-electron-buttons ">
        {/* {pipEnabled && (
          <button
            onClick={pipWindow}
            style={styles.button}
            className="title-bar-electron-buttons-pip"
          >
            {windowPip ? <FiMaximize /> : <FaRegWindowMinimize />}
          </button>
        )} */}
        <button onClick={completeMinimizeWindow} style={styles.button} title="Minimize" aria-label="Minimize" >
          <FaRegWindowMinimize />
        </button>
        {pipEnabled && (
          <button
            onClick={()=>{
              minimizeWindow();
              // if(isPipWindow) {
              //   // setIsPipWindow(false);
              //   minimizeWindow();
              // } else {
              //   maximizeWindow();
              // }
            }}
            style={styles.button}
            className="title-bar-electron-buttons-maximize"
            title={windowMaximized ? "Enable PIP" : "Exit PIP"}
          >
            {windowMaximized ? <BsPip/> : <FiMaximize />}
          </button>
        )}

        {!isPipWindow && (
          <button
            // onClick={closeWindow}
            onClick={() => setIsCloseMeetingModalOpen(true)}
            style={styles.button}
            className="title-bar-electron-buttons-close"
            title="Close"
          >
            <IoMdClose />
          </button>
        )}
      </div>
      <Modal
        open={isCloseMeetingModalOpen}
        onOk={() => {
          closeWindow();
          setIsCloseMeetingModalOpen(false);
        }}
        onCancel={() => setIsCloseMeetingModalOpen(false)}
        footer={null}
        className="close-meeting-modal"
      >
        <p>Are you sure you want to close the meeting?</p>
        <div className="close-meeting-modal-buttons">
          <Button onClick={() => setIsCloseMeetingModalOpen(false)}>No</Button>
          <Button type="primary" onClick={() => {
            closeWindow();
            setIsCloseMeetingModalOpen(false);
          }}>Yes</Button>
        </div>
      </Modal>
    </div>
  );
}

export default TitleBar;
