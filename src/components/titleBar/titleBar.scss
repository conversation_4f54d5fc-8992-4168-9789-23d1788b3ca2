.title-bar-electron {
  &-app-name {
  }
  &-buttons {
    &-pip {
      &-window {
        // arrange the items according to given design
        display: flex;
        justify-content: center;
        .title-bar-electron-buttons-pip {
          order: 1;
        }
        .title-bar-electron-buttons-close {
          order: 2;
        }
      }
      svg {
        width: 20px;
        height: 20px;
      }
    }
    &-maximize {
      svg {
        width: 18px;
        height: 18px;
      }
    }
    &-close {
      svg {
        width: 22px;
        height: 22px;
      }
    }
    button {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.close-meeting-modal {
    .ant-modal-content{
        border-radius: 9px;
    }
    .ant-modal-close-icon{
        color: white;
    }
    &-buttons{
        display: flex;
        justify-content: center;
        gap: 1rem;
        .ant-btn{
            border-radius: 6px;
        }
    }
  .ant-modal-body {
    padding-top: 2rem !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #000;
    border-radius: 8px;
    border: 1px solid #fff;
    p{
        color: white;
        font-size: 18px;
    }
  }
}
