import React, { useEffect, useState } from "react";
import { Button, Modal } from "antd";
import "../styles/NotificationModal.scss";
import "../styles/LiveStreamModal.scss";
import "../styles/index.scss";

export function NotificationModal({
  notificationVisible,
  setNotificationVisible,
  content,
  action,
  room,
}) {
  const [isMessage, setIsMessage] = useState(false);

  useEffect(() => {
    if (action === "send_private_message") {
      setIsMessage(true);
      const timer = setTimeout(() => {
        setNotificationVisible(false);
      }, 10000); // Close modal after 10 seconds

      // Clean up the timer when the component unmounts or action changes
      return () => clearTimeout(timer);
    } else {
      setIsMessage(false);
    }
  }, [action, setNotificationVisible]);

  const handleCancel = () => {
    setNotificationVisible(false);
  };

  const handleAction = () => {
    if (action === "unmute") {
      room.localParticipant.setMicrophoneEnabled(true);
    } else if (action === "videoOn") {
      room.localParticipant.setCameraEnabled(true);
    }
    setNotificationVisible(false);
  };

  return (
    <Modal
      title={null}
      open={notificationVisible}
      onCancel={handleCancel}
      bodyStyle={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        backgroundColor: "#242424",
        color: "white",
      }}
      footer={null}
      // style={{ top: isMessage ? 20 : 0 }}
      className="nt-modal primary-font"
      width={isMessage ? 300 : 600}
    >
      <div className="nt-parent-container">
        {isMessage ? (
          <>
            <div className="nt-message-title">{content.title}</div>
            <div className="nt-message-content">{content.message}</div>
          </>
        ) : (
          <>
            <div className="nt-title">{content.title}</div>
            <div className="nt-button-container ">
              <Button
                type="primary"
                size="large"
                className="ls-button nt-button-cancel "
                onClick={() => setNotificationVisible(false)}
                ghost
              >
                Reject
              </Button>
              <Button
                type="primary"
                size="large"
                className="ls-button"
                onClick={() => handleAction()}
              >
                Accept
              </Button>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
}
