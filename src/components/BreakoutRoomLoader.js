import React from "react";
import <PERSON><PERSON> from "lottie-react";
import { ReactComponent as LoadingIcon } from "../assets/icons/Loading.svg";
import "../styles/Prejoin.scss";

export function BreakoutRoomLoader({ heading, description, isLoading, icon, rootClass }) {
  return (
    <div>
      <div className={`loading-parent-container ${rootClass}`}>
        <div className="loading-title">{heading}</div>
        {isLoading === true ? (icon ? <Lottie
              animationData={icon}
              loop // Set to true for continuous loop, false to play once
              style={{ width: 600, height: 400 }} // Adjust size as needed
              /> : <LoadingIcon className="rotate" />) : null}
        <div className="loading-description">{description}</div>
      </div>
    </div>
  );
}
