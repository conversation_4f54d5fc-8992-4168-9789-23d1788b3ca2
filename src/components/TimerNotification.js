import { notification } from "antd";
import React from "react";
import { MdOutlineTimer } from "react-icons/md";
import "../styles/BreakoutRoomDrawer.scss";

export default function TimerNotification({
    timerSeconds,
    setTimerSeconds,
}) {
  setTimerSeconds(300);
  const openTimerNotification = () => {
    let seconds = timerSeconds; // 5 minutes in seconds

    const updateNotification = () => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      notification.info({
        key: "timer",
        message: "",
        description: `${minutes.toString().padStart(2, "0")}:${remainingSeconds
          .toString()
          .padStart(2, "0")}`,
        duration: 0, // Prevent auto close
        placement: "top",
        icon: <MdOutlineTimer />,
        className: "br-timer",
      });
    };

    updateNotification(); // Initialize notification

    const interval = setInterval(() => {
      seconds -= 1;
      updateNotification();

      if (seconds <= 0) {
        clearInterval(interval);
        notification.success({
          message: "Timer Completed",
          description: "The 5-minute timer has ended.",
        });
        notification.close("timer");
      }
    }, 1000);
  };
  return (
    <div onClick={openTimerNotification}>
      Timer
    </div>
  );
}
