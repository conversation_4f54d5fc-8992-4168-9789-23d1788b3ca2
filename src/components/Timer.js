/* eslint-disable */
import React, { useState, useEffect, useRef } from "react";
import { Popover } from "antd";
import "../styles/Timer.scss";

// Function to display the start and end time exactly as provided
const formatDate = (date) => {
  return new Date(date).toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

// Function to format time in hh:mm:ss
const formatTime = (t) => {
  const hours = Math.floor(t / 3600)
    .toString()
    .padStart(2, "0");
  const minutes = Math.floor((t % 3600) / 60)
    .toString()
    .padStart(2, "0");
  const seconds = (t % 60).toString().padStart(2, "0");
  return `${hours}:${minutes}:${seconds}`;
};

export function Timer({ meetingDetails }) {
  const [time, setTime] = useState(0);
  const [visible, setVisible] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0); // Time elapsed in seconds
  const [remainingTime, setRemainingTime] = useState(0); // Time remaining in seconds

  const { current_session_start_time, end_date } = meetingDetails || {};
  
  // Ref to store the initial start time
  const startTimeRef = useRef(
    current_session_start_time
      ? new Date(current_session_start_time).getTime()
      : Date.now() // Initialize only once
  );

  const startTime = startTimeRef.current; // Use the stored start time
  const endTime = new Date(end_date).getTime();

  // Format the start time for display
  const startDisplayTime = formatDate(startTime);

  useEffect(() => {
    // Increment the timer displayed at the top every second
    const timeInterval = setInterval(() => {
      setTime((prevTime) => prevTime + 1);
    }, 1000);

    // Calculate elapsed and remaining time
    const elapsedAndRemainingInterval = setInterval(() => {
      const now = Date.now();
      const elapsed = Math.max(0, Math.floor((now - startTime) / 1000));
      const remaining = Math.max(0, Math.floor((endTime - now) / 1000));
      setElapsedTime(elapsed);
      setRemainingTime(remaining);
    }, 1000);

    return () => {
      clearInterval(timeInterval); // Cleanup timer
      clearInterval(elapsedAndRemainingInterval); // Cleanup elapsed and remaining time calculation
    };
  }, [startTime, endTime]);

  return (
    <>
      <Popover
        content={
          <div className="timer-popover-content">
            <div className="timer-popover-content-time">
              <div>
                <p>Start Time</p>
                <span>{startDisplayTime}</span>
              </div>
              <div>
                <p>Time Lapsed</p>
                <span>{formatTime(elapsedTime)}</span>
              </div>
            </div>
            <div className="timer-popover-content-time">
              <div>
                <p>End Time</p>
                <span>{formatDate(end_date)}</span>
              </div>
              <div>
                <p>Remaining Time</p>
                <span>{formatTime(remainingTime)}</span>
              </div>
            </div>
          </div>
        }
        title={null}
        open={visible}
        onOpenChange={setVisible}
        overlayClassName="timer-popover"
        // action="click"
        placement="bottomRight"
      >
        {formatTime(time)}
      </Popover>
    </>
  );
}
