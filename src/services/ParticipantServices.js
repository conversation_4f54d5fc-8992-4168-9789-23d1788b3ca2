import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";
import { parseMetadata } from "../utils/helper";

export const ParticipantService = {
  lobbyParticipantStatusUpdate: async (
    meetingId,
    requestId,
    status,
    coHostToken,
    user,
    devMode
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.status_update_participant_lobby.method,
        endpoint: Endpoints.status_update_participant_lobby.url,
        payload: {
          meeting_uid: meetingId,
          request_id: requestId,
          is_admit: status,
        },
        token: coHostToken,
        devmode: devMode,
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error updating participant status in lobby", {
          response,
          payload: {
            meeting_uid: meetingId,
            request_id: requestId,
            is_admit: status,
          },
          endpoint: Endpoints.status_update_participant_lobby.url,
          user,
        });
      } else {
        datadogLogs.logger.info(
          "Success updating participant status in lobby",
          {
            response,
            payload: {
              meeting_uid: meetingId,
              request_id: requestId,
              is_admit: status,
            },
            endpoint: Endpoints.status_update_participant_lobby.url,
            user,
          }
        );
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  removeParticipant: async (meetingId, participantId, coHostToken,user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.remove_participant.method,
        endpoint: Endpoints.remove_participant.url,
        payload: {
          meeting_uid: meetingId,
          participant_id: participantId,
        },
        token: coHostToken,
        devmode: devMode,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error removing participant", {
          response,
          payload: {
            meeting_uid: meetingId,
            participant_id: participantId,
          },
          endpoint: Endpoints.remove_participant.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success removing participant", {
          response,
          payload: {
            meeting_uid: meetingId,
            participant_id: participantId,
          },
          endpoint: Endpoints.remove_participant.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  makeCoHost: async (meetingId, participant,apiToken,user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.assign_co_host.method,
        endpoint: Endpoints.assign_co_host.url,
        payload: {
          meeting_uid: meetingId,
          participant_identity: participant.identity,
          is_co_host: parseMetadata(participant.metadata)?.role_name !== "cohost",
        },
        token: apiToken,
        devmode:devMode
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error making participant co-host", {
          response,
          payload: {
            meeting_uid: meetingId,
            participant_identity: participant.identity,
            is_co_host: parseMetadata(participant.metadata)?.role_name !== "cohost",
          },
          endpoint: Endpoints.assign_co_host.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success making participant co-host", {
          response,
          payload: {
            meeting_uid: meetingId,
            participant_identity: participant.identity,
            is_co_host: parseMetadata(participant.metadata)?.role_name !== "cohost",
          },
          endpoint: Endpoints.assign_co_host.url,
          user,
        });
      }
      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  updateParticipantName: async (meetingId, participant, name, apiToken,user,devMode)=>{
    try {
      const response = await APIrequest({
        method: Endpoints.participant_name_update.method,
        endpoint: Endpoints.participant_name_update.url,
        payload: {
          meeting_uid: meetingId,
          participant_identity: participant.identity,
          new_name: name,
        },
        token: apiToken,
        devmode:devMode
      })
      if(response.success === 0){
        datadogLogs.logger.error("Error updating participant name", {
          response,
          payload: {
            meeting_uid: meetingId,
            participant_identity: participant.identity,
            new_name: name,
          },
          endpoint: Endpoints.participant_name_update.url,
          user,
        });
      }else{
        datadogLogs.logger.info("Success updating participant name", {
          response,
          payload: {
            meeting_uid: meetingId,
            participant_identity: participant.identity,
            new_name: name,
          },
          endpoint: Endpoints.participant_name_update.url,
          user,
        });
      }
    } catch (error) {
      datadogLogs.logger.error("Error updating participant name", {
        error,
        payload: {
          meeting_uid: meetingId,
          participant_identity: participant.identity,
          new_name: name,
        },
        endpoint: Endpoints.participant_name_update.url,
        user,
      });
    }
  }
};
