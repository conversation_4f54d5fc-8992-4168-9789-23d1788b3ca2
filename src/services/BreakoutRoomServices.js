import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";
import { parseMetadata } from "../utils/helper";

export const BreakoutRoomService = {
  getBreakoutRoomToken: async (participant, meetingId, devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.get_breakout_room_token.method,
        endpoint: Endpoints.get_breakout_room_token.url,
        payload: {
          meeting_uid: meetingId,
          identity: participant?.identity,
          role: parseMetadata(participant?.metadata)?.role_name,
          name: participant?.name,
        },
        devmode: devMode,
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error getting breakout room token", {
          response,
          payload: {
            meeting_uid: meetingId,
            identity: participant?.identity,
            role: parseMetadata(participant?.metadata)?.role_name,
            name: participant?.name,
          },
          endpoint: Endpoints.get_breakout_room_token.url,
          user: participant?.participantInfo,
          devMode
        });
      } else {
        datadogLogs.logger.info("Success getting breakout room token", {
          response,
          payload: {
            meeting_uid: meetingId,
            identity: participant?.identity,
            role: parseMetadata(participant?.metadata)?.role_name,
            name: participant?.name,
          },
          endpoint: Endpoints.get_breakout_room_token.url,
          user: participant?.participantInfo,
          devMode
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getBreakoutRoomDetail: async (meetingId, token,user, devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.get_breakout_room_detail(meetingId).method,
        endpoint: Endpoints.get_breakout_room_detail(meetingId).url,
        token,
        devmode: devMode,
      });
      if (response.success === 0) {
        // console.log("Error getting breakout room details", response);
        datadogLogs.logger.error("Error getting breakout room details", {
          response,
          endpoint: Endpoints.get_breakout_room_detail(meetingId).url,
          user,
          devMode
        });
      } else {
        datadogLogs.logger.info("Success getting breakout room details", {
          response,
          endpoint: Endpoints.get_breakout_room_detail(meetingId).url,
          user,
          devMode
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  moveParticipantToBreakoutRoom: async (
    participantIds,
    fromMeetingId,
    toMeetingId,
    token,
    user,
    devMode
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.move_participant_to_breakout_room.method,
        endpoint: Endpoints.move_participant_to_breakout_room.url,
        payload: {
          from_meeting_uid: fromMeetingId,
          to_meeting_uid: toMeetingId,
          participant_ids: participantIds,
        },
        token,
        devmode: devMode,
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error moving participant to breakout room", {
          response,
          payload: {
            from_meeting_uid: fromMeetingId,
            to_meeting_uid: toMeetingId,
            participant_ids: participantIds,
          },
          endpoint: Endpoints.move_participant_to_breakout_room.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success moving participant to breakout room", {
          response,
          payload: {
            from_meeting_uid: fromMeetingId,
            to_meeting_uid: toMeetingId,
            participant_ids: participantIds,
          },
          endpoint: Endpoints.move_participant_to_breakout_room.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  saveBreakoutRoomConfig: async (
    meetingId,
    breakoutRoomIds,
    assigment,
    timeout,
    token,
    user,
    devMode
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.save_breakout_room_config.method,
        endpoint: Endpoints.save_breakout_room_config.url,
        payload: {
          main_room_meeting_uid: meetingId,
          breakout_room_uids: breakoutRoomIds,
          room_assignment: assigment,
          auto_timeout: timeout,
        },
        token,
        devmode: devMode,
      });

      if (response.success === 0) {
        // console.log("Error saving breakout room config", response);
        datadogLogs.logger.error("Error saving breakout room config", {
          response,
          payload: {
            main_room_meeting_uid: meetingId,
            breakout_room_uids: breakoutRoomIds,
            room_assignment: assigment,
            auto_timeout: timeout,
          },
          endpoint: Endpoints.save_breakout_room_config.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success saving breakout room config", {
          response,
          payload: {
            main_room_meeting_uid: meetingId,
            breakout_room_uids: breakoutRoomIds,
            room_assignment: assigment,
            auto_timeout: timeout,
          },
          endpoint: Endpoints.save_breakout_room_config.url,
          user,
        });
      }


      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getBreakoutRoomConfig: async (meetingId,token, user, devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.get_breakout_room_config(meetingId).method,
        endpoint: Endpoints.get_breakout_room_config(meetingId).url,
        token,
        devmode: devMode,
      });
      if (response.success === 0) {
        // console.log("Error getting breakout room config", response);
        datadogLogs.logger.error("Error getting breakout room config", {
          response,
          endpoint: Endpoints.get_breakout_room_config(meetingId).url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success getting breakout room config", {
          response,
          endpoint: Endpoints.get_breakout_room_config(meetingId).url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
};
