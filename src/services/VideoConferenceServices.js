import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";
// import { apiLogger } from "../utils/helper";

export const VideoConferenceService = {
  sipConnection: async (id,token, user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.sip_connection.method,
        endpoint: Endpoints.sip_connection.url,
        payload: {
          meeting_uid: id,
        },
        token,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error connecting to SIP", {
          response,
          payload: {
            meeting_uid: id,
          },
          endpoint: Endpoints.sip_connection.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success connecting to SIP", {
          response,
          payload: {
            meeting_uid: id,
          },
          endpoint: Endpoints.sip_connection.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  extendMeeting: async (extended, id, token, devmode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.extend_meeting.method,
        endpoint: Endpoints.extend_meeting.url,
        payload: {
          is_extend_time: extended,
          meeting_uid: id,
        },
        token,
        devmode: devmode,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error extending meeting", {
          response,
          payload: {
            is_extend_time: extended,
            meeting_uid: id,
          },
          endpoint: Endpoints.extend_meeting.url,
          // user,
        });
      } else {
        datadogLogs.logger.info("Success extending meeting", {
          response,
          payload: {
            is_extend_time: extended,
            meeting_uid: id,
          },
          endpoint: Endpoints.extend_meeting.url,
          // user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
};
