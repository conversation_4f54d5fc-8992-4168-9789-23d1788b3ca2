import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";
// import { apiLogger } from "../utils/helper";

export const WhiteboardService = {
  updateWhiteboardStatus: async (id, status, meetingId,token, user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.update_whiteboard_status.method,
        endpoint: Endpoints.update_whiteboard_status.url,
        payload: {
          whiteboard_id: id,
          status,
        },
        token,
        devmode:devMode
      });
      if (response.success === 0) {
        // console.log("Error getting breakout room token", response);
        datadogLogs.logger.error("Error updating whiteboard status", {
          response,
          payload: {
            whiteboard_id: id,
            status,
            meetingId,
          },
          endpoint: Endpoints.update_whiteboard_status.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success updating whiteboard status", {
          response,
          payload: {
            whiteboard_id: id,
            status,
            meetingId,
          },
          endpoint: Endpoints.update_whiteboard_status.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  saveWhiteboard: async (meetingId, whiteboadData,token, user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.save_whiteboard_data.method,
        endpoint: Endpoints.save_whiteboard_data.url,
        payload: {
          meeting_uid: meetingId,
          whiteboard_json: whiteboadData,
        },
        token,
        devmode:devMode
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error saving whiteboard data", {
          response,
          payload: {
            meeting_uid: meetingId,
            whiteboard_json: whiteboadData,
          },
          endpoint: Endpoints.save_whiteboard_data.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success saving whiteboard data", {
          response,
          payload: {
            meeting_uid: meetingId,
            whiteboard_json: whiteboadData,
          },
          endpoint: Endpoints.save_whiteboard_data.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getWhiteboardDetail: async (meetingId,token, user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.get_multiple_whiteboard_data(meetingId).method,
        endpoint: Endpoints.get_multiple_whiteboard_data(meetingId).url,
        token,
        devmode:devMode
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error getting whiteboard data of a meeting", {
          response,
          endpoint: Endpoints.get_multiple_whiteboard_data(meetingId).url,
          user,
        });
      } else {
        datadogLogs.logger.info(
          "Success getting whiteboard data of a meeting",
          {
            response,
            endpoint: Endpoints.get_multiple_whiteboard_data(meetingId).url,
            user,
          }
        );
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
};
