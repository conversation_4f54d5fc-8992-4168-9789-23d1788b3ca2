import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";

export const SettingsMenuServices = {
  startCloudRecording: async (meetingId, token,user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.recording_start.method,
        endpoint: Endpoints.recording_start.url,
        payload: {
          meeting_uid: meetingId,
        },
        token,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error starting cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.recording_start.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.recording_start.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  stopCloudRecording: async (meetingId, egressId, token,user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.recording_stop.method,
        endpoint: Endpoints.recording_stop.url,
        payload: {
          meeting_uid: meetingId,
          egress_id: egressId,
        },
        token,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error stopping cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.recording_stop.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success stopping cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.recording_stop.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  reportAProblem: async (
    id,
    preferredVideoServerId,
    userDescription,
    audioIssue,
    videoIssue,
    captionIssue,
    user,
    devMode
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.report_problem.method,
        endpoint: Endpoints.report_problem.url,
        payload: {
          meeting_uid: id,
          preferred_video_server_id: preferredVideoServerId,
          user_description: userDescription,
          audio_issue: audioIssue,
          video_issue: videoIssue,
          caption_issue: captionIssue,
        },
        devmode: devMode,
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error reporting a problem", {
          response,
          payload: {
            meeting_uid: id,
            preferred_video_server_id: preferredVideoServerId,
            user_description: userDescription,
            audio_issue: audioIssue,
            video_issue: videoIssue,
            caption_issue: captionIssue,
          },
          endpoint: Endpoints.report_problem.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success reporting a problem", {
          response,
          payload: {
            meeting_uid: id,
            preferred_video_server_id: preferredVideoServerId,
            user_description: userDescription,
            audio_issue: audioIssue,
            video_issue: videoIssue,
            caption_issue: captionIssue,
          },
          endpoint: Endpoints.report_problem.url,
          user,
        });
      }
      return response;

    } catch (error) {
      throw new Error(error);
    }
  },
  livestreamStart: async (meetingId, url, streamKey,token,user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.livestream_start.method,
        endpoint: Endpoints.livestream_start.url,
        payload: {
          meeting_uid: meetingId,
          stream_rtmp_urls: [`${url}/${streamKey}`],
        },
        token,
        devmode:devMode
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error starting livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            // stream_rtmp_urls: [`${url}/${streamKey}`],
          },
          endpoint: Endpoints.livestream_start.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            // stream_rtmp_urls: [`${url}/${streamKey}`],
          },
          endpoint: Endpoints.livestream_start.url,
          user,
        });
      }
      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  livestreamStop: async (meetingId, egressId,token,user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.livestream_stop.method,
        endpoint: Endpoints.livestream_stop.url,
        payload: {
          meeting_uid: meetingId,
          egress_id: egressId,
        },
        token,
        devmode:devMode
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error stopping livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.livestream_stop.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success stopping livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.livestream_stop.url,
          user,
        });
      }
      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  startLiveTranscription: async (meetingId,token, user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.start_live_transcription.method,
        endpoint: Endpoints.start_live_transcription.url,
        payload: {
          meeting_uid: meetingId,
        },
        token,
        devmode: devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error starting live transcription", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.start_live_transcription.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting live transcription", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.start_live_transcription.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  setLiveTranscriptionDetail: async (meetingId, languageCode, language, isEnabled,token,user,devMode)=>{
    try {
      const response = await APIrequest({
        method: Endpoints.setTranscriptionDetail.method,
        endpoint : Endpoints.setTranscriptionDetail.url,
        payload:{
          meeting_uid: meetingId,
          transcription_enable: isEnabled,
          transcription_lang_iso: languageCode,
          transcription_lang_title: language
        },
        token,
        devmode:devMode
      })
      if (response.success === 0) {
        datadogLogs.logger.error("Error setting live transcription detail", {
          response,
          payload: {
            meeting_uid: meetingId,
            transcription_enable: isEnabled,
            transcription_lang_iso: languageCode,
            transcription_lang_title: language,
          },
          endpoint: Endpoints.setTranscriptionDetail.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success setting live transcription detail", {
          response,
          payload: {
            meeting_uid: meetingId,
            transcription_enable: isEnabled,
            transcription_lang_iso: languageCode,
            transcription_lang_title: language,
          },
          endpoint: Endpoints.setTranscriptionDetail.url,
          user,
        });
      }
      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getVirtualBackground: async (apiToken,user,devMode)=>{
    try {
      const response = await APIrequest({
        method: Endpoints.get_virtual_background.method,
        endpoint: Endpoints.get_virtual_background.url,
        token: apiToken,
        devmode:devMode
      })
      if (response.success === 0) {
        datadogLogs.logger.error("Error getting virtual background", {
          response,
          endpoint: Endpoints.get_virtual_background.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success getting virtual background", {
          response,
          endpoint: Endpoints.get_virtual_background.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  uploadVirtualBackground: async (file,token, user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.set_virtual_background.method,
        endpoint: Endpoints.set_virtual_background.url,
        payload: file,
        token,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error uploading virtual background", {
          response,
          payload: file,
          endpoint: Endpoints.set_virtual_background.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success uploading virtual background", {
          response,
          payload: file,
          endpoint: Endpoints.set_virtual_background.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  deleteVirtualBackground: async (id,token, user,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.delete_virtual_background.method,
        endpoint: Endpoints.delete_virtual_background.url,
        payload: {
          id,
        },
        token,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error deleting virtual background", {
          response,
          payload: {
            id,
          },
          endpoint: Endpoints.delete_virtual_background.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success deleting virtual background", {
          response,
          payload: {
            id,
          },
          endpoint: Endpoints.delete_virtual_background.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getLiveTranscriptionDetail: async (langId, textContent, apiToken,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.getTranscriptionDetail.method,
        endpoint: Endpoints.getTranscriptionDetail.url,
        payload: {
          target_language: langId,
          text: textContent,
        },
        token: apiToken,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting live transcription detail", {
          response,
          payload: {
            target_language: langId,
            text: textContent,
          },
          endpoint: Endpoints.getTranscriptionDetail.url,
        });
      } else {
        datadogLogs.logger.info("Success getting live transcription detail", {
          response,
          payload: {
            target_language: langId,
            text: textContent,
          },
          endpoint: Endpoints.getTranscriptionDetail.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  translateTextService: async (bodyData, apiToken,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.translateText.method,
        endpoint: Endpoints.translateText.url,
        payload: bodyData,
        // token: apiToken,
        devmode: devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error translating text", {
          response,
          payload: bodyData,
          endpoint: Endpoints.translateText.url,
        });
      } else {
        datadogLogs.logger.info("Success translating text", {
          response,
          payload: bodyData,
          endpoint: Endpoints.translateText.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  startRecordingConsent: async (meetingId, sessionId,attendance_id, user, meetingConsentStatus,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.startRecordingConsent.method,
        endpoint: Endpoints.startRecordingConsent.url,
        payload: {
          meeting_uid: meetingId,
          session_id: sessionId,
          meeting_consent_start: meetingConsentStatus,
          attendance_id: attendance_id,
        },
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error starting recording consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            meeting_consent_start: meetingConsentStatus,
          },
          endpoint: Endpoints.startRecordingConsent.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting recording consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            meeting_consent_start: meetingConsentStatus,
          },
          endpoint: Endpoints.startRecordingConsent.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  updateParticipantConsent: async (meetingId, sessionId, user, attendenceId, participantConsentStatus,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.updateParticipantConsent.method,
        endpoint: Endpoints.updateParticipantConsent.url,
        payload: {
          meeting_uid: meetingId,
          session_id: sessionId,
          is_accepted: participantConsentStatus,
          attendance_id: attendenceId,
        },
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error updating participant consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            is_accepted: participantConsentStatus,
            attendence_id: attendenceId,
          },
          endpoint: Endpoints.updateParticipantConsent.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success updating participant consent", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
            is_accepted: participantConsentStatus,
            attendance_id: attendenceId,
          },
          endpoint: Endpoints.updateParticipantConsent.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getParticipantConsentList: async (meetingId, sessionId,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.getParticipantConsentList(meetingId, sessionId).method,
        endpoint: Endpoints.getParticipantConsentList(meetingId, sessionId).url,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting participant consent list", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
          },
          endpoint: Endpoints.getParticipantConsentList(meetingId, sessionId).url,
        });
      } else {
        datadogLogs.logger.info("Success getting participant consent list", {
          response,
          payload: {
            meeting_uid: meetingId,
            session_id: sessionId,
          },
          endpoint: Endpoints.getParticipantConsentList(meetingId, sessionId).url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getMeetingSession: async (meetingId,devMode) => {
    try {
      const response = await APIrequest({
        method: Endpoints.getMeetingSession(meetingId).method,
        endpoint: Endpoints.getMeetingSession(meetingId).url,
        devmode:devMode
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting meeting session", {
          response,
          payload: { meeting_uid: meetingId },
        });
      } else {
        datadogLogs.logger.info("Success getting meeting session", {
          response,
          payload: { meeting_uid: meetingId },
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
};
