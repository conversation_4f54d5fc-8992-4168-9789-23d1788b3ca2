import "@livekit/components-styles";
// import React, { useEffect, useState } from "react";
import * as React from "react";
import moment from "moment";

import { LiveKitRoom } from "@livekit/components-react";
import { Room } from "livekit-client";
import { defaultUserChoices } from "@livekit/components-core";
import { pdfjs } from "react-pdf";
import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";

import isElectron from "is-electron";
import { Prejoin } from "./customFabs/PreJoin";
import { VideoConference } from "./customFabs/VideoConference";

import { Loader } from "./components/Loader";
import { SettingsControlButton } from "./components/settings/SettingsControlButton";

import { PrejoinService } from "./services/PrejoinServices";
import { BreakoutRoomLoader } from "./components/BreakoutRoomLoader";
import TitleBar from "./components/titleBar";
import LoadingIcon from "./customFabs/icons/BreakoutAnimation2.json";

import {
  decoder,
  setLocalStorage,
  getLocalStorage,
  getMediaPermissions,
} from "./utils/helper";
import { constants } from "./utils/constants";
import "antd/dist/antd.min.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

function DaakiaVC({
  id,
  licenseKey,
  websiteBaseUrl = "https://daakia.co.in",
  apiToken = null,
  eventHandler = null,
  onClose = null,
  inviteUrl = null,
  meetingEndedPageUrl = `${websiteBaseUrl}/meeting/ended`,
  meetingLeftPageUrl = `${websiteBaseUrl}/meeting/left`,
  participantRemovedPageUrl = `${websiteBaseUrl}/meeting/removed`,
  meetingFeatures = {
    id: 210,
    subscription_id: 49,
    is_active: 1,
    audio_video_conference: "1000000",
    meet_duration: "1440",
    cloud_storage: "100",
    save_cloud: 1,
    international_phone: 1,
    host_meeting_mobile: 1,
    conference_chat: 1,
    whiteboard: 1,
    noise_cancellation: 1,
    record_meeting: 1,
    poll: 1,
    raise_hand: 1,
    breakout_room: 1,
    screen_sharing: 1,
    voice_transcription: 1,
    voice_text_translation: 1,
    live_stream: 1,
    share_youtube: 1,
    track_attendance: 1,
    mute_participant: 1,
    disable_camera: 1,
    compatibility: 1,
    mobile_support: 1,
    encryption: 1,
    lobby: 1,
    protected_meeting: 1,
    spam_protection: 1,
    translation: 1,
    video_translation: 1,
    is_basic: conferenceFeatures?.auto_end_conference === 1 ? 0 : 1,
    auto_end_conference: conferenceFeatures
      ? conferenceFeatures.auto_end_conference
      : null,
    branding_logo_url: conferenceFeatures
      ? conferenceFeatures.branding_logo_url
      : null,
    branding_icon_url: conferenceFeatures
      ? conferenceFeatures.branding_icon_url
      : null,
    branding_app_title: conferenceFeatures
      ? conferenceFeatures.app_title
      : null,
    branding_enabled: conferenceFeatures
      ? conferenceFeatures.branding_enabled
      : null,
    configurations: {
      auto_end_conference: conferenceFeatures
        ? conferenceFeatures.auto_end_conference
        : null,
      branding_logo_url: conferenceFeatures
        ? conferenceFeatures.branding_logo_url
        : null,
      branding_icon_url: conferenceFeatures
        ? conferenceFeatures.branding_icon_url
        : null,
      branding_app_title: conferenceFeatures
        ? conferenceFeatures.app_title
        : null,
      branding_enabled: conferenceFeatures
        ? conferenceFeatures.branding_enabled
        : null,
      ask_recording_request: conferenceFeatures
        ? conferenceFeatures.ask_recording_request
        : "none",
      storage_unit: conferenceFeatures ? conferenceFeatures.storage_unit : null,
      external_storage_unit_config: conferenceFeatures
        ? conferenceFeatures.external_storage_unit_config
        : null,
      show_meeting_info: conferenceFeatures
        ? conferenceFeatures.show_meeting_info
        : 0,
      allow_multiple_cohost: conferenceFeatures
        ? conferenceFeatures.allow_multiple_cohost
        : 0,
      enable_reaction: conferenceFeatures
        ? conferenceFeatures.enable_reaction
        : 0,
      enable_private_chat: conferenceFeatures
        ? conferenceFeatures.enable_private_chat
        : 0,
      enable_chat: conferenceFeatures ? conferenceFeatures.enable_chat : 0,
      allow_profile_edit_by_host: true,
      allow_profile_edit_by_self: true,
    },
  },
  devMode = true,
  enableLog = true,
}) {
  const maxHeight = Math.min(
    window.screen.height * window.devicePixelRatio,
    1620
  );
  const maxWidth = Math.min(
    window.screen.width * window.devicePixelRatio,
    2880
  );

  const [isWebinarMode, setIsWebinarMode] = React.useState(false);
  const [serverDetails, setServerDetails] = React.useState({});
  const [preJoinShow, setPreJoinShow] = React.useState(true);
  const [isHost, setIsHost] = React.useState(false);
  const [decodedId, setDecodedId] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = React.useState(false);
  const [isInvalidMeeting, setIsInvalidMeeting] = React.useState(false);
  const [meetingDetails, setMeetingDetails] = React.useState({});
  const [clientPreferedServerId, setClientPreferedServerId] =
    React.useState("ap1"); // eslint-disable-line no-unused-vars
  const [userChoices, setUserChoices] = React.useState({});
  const [isLobbyMode, setIsLobbyMode] = React.useState(false);
  const [isMeetingFinished, setIsMeetingFinished] = React.useState(false);
  const [isUsingBreakoutRoom, setIsUsingBreakoutRoom] = React.useState(false);
  const [isMovingToRoom, setIsMovingToRoom] = React.useState(false);
  const [movingRoomToken, setMovingRoomToken] = React.useState(null);
  const [meetingUserChoices, setMeetingUserChoices] = React.useState({});
  const [isdataDogInitialized, setIsDataDogInitialized] = React.useState(false);
  const [screenShareSources, setScreenShareSources] = React.useState([]);
  const [isPipWindow, setIsPipWindow] = React.useState(false);
  const isElectronApp = isElectron();
  const [isValidating, setIsValidating] = React.useState(true);
  const [isLicenseKeyValid, setIsLicenseKeyValid] = React.useState(true);
  const [isRoomFull, setIsRoomFull] = React.useState(false);
  const [isSelfVideoMirrored, setIsSelfVideoMirrored] = React.useState(false);

  const [room, setRoom] = React.useState(
    new Room({
      dynacast: true,
      adaptiveStream: true,
      audioCaptureDefaults: {
        echoCancellation: true,
        noiseSuppression: true,
      },
      publishDefaults: {
        screenShareEncoding: {
          maxBitrate: 1_000_000,
          maxFramerate: 5,
        },
        screenShareSimulcastLayers: [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 1,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ],
      },
    })
  );

  React.useEffect(() => {
    room.prepareConnection("https://ap1sfu-prod.daakia.co.in");
  }, []);

  // Add validation of license key
  React.useEffect(() => {
    setTimeout(() => {
      setIsValidating(false);
    }, 1000);
    setIsLicenseKeyValid(true);
  }, []);

  // Get the previously set user choices
  React.useEffect(() => {
    const item = localStorage.getItem("lk-user-choices");
    if (!item) {
      setUserChoices(defaultUserChoices);
    } else {
      setUserChoices(JSON.parse(item));
    }
  }, []);

  // Decode the meeting id
  React.useEffect(() => {
    if (id) {
      setDecodedId(() => decoder(id));
    }
  }, [id]);

  // Datadog log initialize
  React.useEffect(() => {
    if (!enableLog) return;
    if (!isdataDogInitialized) {
      datadogLogs.init({
        clientToken: constants.DATA_DOG_TOKEN,
        site: constants.DATA_DOG_SITE,
        service: constants.DATA_DOG_LOG_SERVICE,
        env: devMode
          ? constants.DATA_DOG_LOG_LOCAL_ENV
          : constants.DATA_DOG_LOG_PROD_ENV,
        // version: __COMMIT_HASH__,
        forwardErrorsToLogs: false,
        sessionSampleRate: 100,
      });
      datadogRum.init({
        applicationId: constants.DATA_DOG_RUM_APPLICATION_ID,
        clientToken: constants.DATA_DOG_RUM_TOKEN,
        // `site` refers to the Datadog site parameter of your organization
        // see https://docs.datadoghq.com/getting_started/site/
        site: constants.DATA_DOG_SITE,
        service: constants.DATA_DOG_RUM_SERVICE,
        env: devMode
          ? constants.DATA_DOG_LOG_LOCAL_ENV
          : constants.DATA_DOG_LOG_PROD_ENV,
        // Specify a version number to identify the deployed version of your application in Datadog
        // version: '1.0.0',
        sessionSampleRate: 100,
        sessionReplaySampleRate: 100,
        trackUserInteractions: true,
        trackResources: true,
        trackLongTasks: true,
        defaultPrivacyLevel: "mask-user-input",
      });

      setIsDataDogInitialized(true);
    }
  }, []);

  // Fetch for screen share sources in desktop app
  React.useEffect(() => {
    const getSourcesOrError = async () => {
      try {
        const sourcesOrError = await window.electronAPI.ipcRenderer.invoke(
          "get-screenshare-sources"
        );
        if (sourcesOrError.error) {
          console.error("Error getting sources:", sourcesOrError.message);
          // Handle error (e.g., show notification, update state)
        } else {
          setScreenShareSources(sourcesOrError);
        }
      } catch (error) {
        console.error(
          "Unexpected error while fetching screen share sources:",
          error
        );
        // Handle unexpected errors
      }
    };

    if (isElectron()) {
      getSourcesOrError();
      getMediaPermissions();
    }
  }, []);

  // Fetch meeting details
  React.useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (!decodedId) return;
      try {
        const response = await PrejoinService.getMeetingDetails(
          decodedId,
          apiToken,
          devMode
        );

        if (response.success === 0) {
          setIsInvalidMeeting(() => true);
          return;
        }
        setIsHost(() => response?.data?.is_host);
        const meetingData = response.data;
        const endDate = moment(meetingData?.end_date);
        const autoMeetingEndSchedule = moment(
          meetingData?.meeting_config?.auto_meeting_end_schedule
        );
        const currentDate = moment();

        if (currentDate.isAfter(endDate)) {
          if (meetingData?.meeting_config?.auto_meeting_end === 1) {
            if (currentDate.isAfter(autoMeetingEndSchedule)) {
              setIsMeetingFinished(true);
              return;
            }
          } else {
            setIsMeetingFinished(true);
            return;
          }
        }

        setIsPasswordProtected(() => response?.data?.is_password);
        setMeetingDetails(() => response?.data);
        setIsWebinarMode(
          () => response?.data?.event_type.toLowerCase() === "webinar"
        );
        setIsLobbyMode(() => response?.data?.is_lobby_mode);
        setLocalStorage(constants.MEETING_DETAILS, response?.data);
        if (
          response?.data?.meeting_logs?.session_participants >=
          Number(meetingFeatures?.audio_video_conference)
        ) {
          setIsRoomFull(true);
        }
      } catch (error) {
        console.error("Error getting meeting details:", error);
        setIsInvalidMeeting(() => true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [decodedId]);

  // Moving room
  React.useEffect(() => {
    if (!room || !isMovingToRoom || !movingRoomToken) return;
    room.disconnect();
    setServerDetails({ ...serverDetails, token: null });
    setRoom(
      new Room({
        dynacast: true,
        adaptiveStream: true,
        audioCaptureDefaults: {
          echoCancellation: true,
          noiseSuppression: true,
        },
        publishDefaults: {
          screenShareEncoding: {
            maxBitrate: 1_000_000,
            maxFramerate: 5,
          },
          screenShareSimulcastLayers: [
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 800_000,
                maxFramerate: 1,
              },
            },
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 400_000,
                maxFramerate: 1,
              },
            },
          ],
        },
      })
    );
    setServerDetails({ ...serverDetails, token: movingRoomToken });
    setMovingRoomToken(null);
    setTimeout(() => {
      setIsMovingToRoom(false);
    }, 3000);
  }, [isMovingToRoom, movingRoomToken]);

  // Keeping the user choices persistent
  React.useEffect(() => {
    if (isUsingBreakoutRoom) {
      const meetingChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
      if (meetingChoice) {
        setMeetingUserChoices(meetingChoice);
      }
    }
  }, [isUsingBreakoutRoom]);

  return !isLicenseKeyValid ? (
    <div className="loader-container">
      <Loader
        heading="Invalid License Key"
        description="Please contact the support team."
        isLoading={false}
      />
    </div>
  ) : isLoading || isValidating ? (
    <div className="loader-container">
      <Loader
        heading="Please wait for a moment"
        description="We are fetching the meeting details for you."
        isLoading
      />
    </div>
  ) : isRoomFull ? (
    <div className="loader-container">
      <Loader
        heading="Room is Full!"
        description={
          isWebinarMode
            ? "The slot for this webinar is full. Please try after some time"
            : "Please contact the host for more information."
        }
        isLoading={false}
      />
    </div>
  ) : isInvalidMeeting ? (
    <div className="loader-container">
      <Loader
        heading="No Meeting Found!"
        description="Please check the meeting link and try again."
        isLoading={false}
      />
    </div>
  ) : isMeetingFinished ? (
    <div className="loader-container">
      <Loader
        heading="Meeting has been Ended!"
        description={
          isHost
            ? "You can start a new meeting."
            : "Please contact the host for more information."
        }
        isLoading={false}
      />
    </div>
  ) : isMovingToRoom ? (
    <div className="loader-container">
      <BreakoutRoomLoader
        heading="Room Hop!"
        description="Just a sec! We're moving you to your next adventure."
        isLoading
        icon={LoadingIcon}
        rootClass="breakout-room-loader"
      />
    </div>
  ) : preJoinShow ? (
    <Prejoin
      setServerDetails={setServerDetails}
      id={decodedId}
      setPreJoinShow={setPreJoinShow}
      // setIsHost={setIsHost}
      isHost={isHost}
      isPasswordProtected={isPasswordProtected}
      meetingDetails={meetingDetails}
      setClientPreferedServerId={setClientPreferedServerId}
      userChoices={userChoices}
      setUserChoices={setUserChoices}
      isLobbyMode={isLobbyMode}
      isWebinarMode={isWebinarMode}
      setIsPipWindow={setIsPipWindow}
      isPipWindow={isPipWindow}
      apiToken={apiToken}
      devMode={devMode}
      isSelfVideoMirrored={isSelfVideoMirrored}
      setIsSelfVideoMirrored={setIsSelfVideoMirrored}
      meetingFeatures={meetingFeatures}
    />
  ) : (
    !preJoinShow &&
    serverDetails.token &&
    serverDetails.serverUrl && (
      <LiveKitRoom
        room={room}
        video={
          isUsingBreakoutRoom
            ? meetingUserChoices.video
            : isWebinarMode && !isHost
            ? false
            : userChoices.videoEnabled
            ? { deviceId: userChoices.videoDeviceId }
            : false
        }
        audio={
          isUsingBreakoutRoom
            ? meetingUserChoices.audio
            : isWebinarMode && !isHost
            ? false
            : userChoices.audioEnabled
            ? { deviceId: userChoices.audioDeviceId }
            : false
        }
        token={serverDetails.token}
        serverUrl={serverDetails.serverUrl}
        data-lk-theme="default"
        style={{ height: "100vh", width: "100vw" }}
      >
        {isElectronApp && (
          <TitleBar
            setIsPipWindow={setIsPipWindow}
            isPipWindow={isPipWindow}
            title={meetingDetails?.event_name}
          />
        )}
        <VideoConference
          room={room}
          SettingsComponent={SettingsControlButton}
          maxHeight={maxHeight}
          maxWidth={maxWidth}
          id={decodedId}
          isHost={isHost}
          meetingDetails={meetingDetails}
          clientPreferedServerId={clientPreferedServerId}
          isMeetingFinished={isMeetingFinished}
          setIsMeetingFinished={setIsMeetingFinished}
          setIsMovingToRoom={setIsMovingToRoom}
          setMovingRoomToken={setMovingRoomToken}
          isMovingToRoom={isMovingToRoom}
          meetingFeatures={meetingFeatures}
          isWebinarMode={isWebinarMode}
          setIsUsingBreakoutRoom={setIsUsingBreakoutRoom}
          token={serverDetails.token}
          isElectronApp={isElectronApp}
          screenShareSources={screenShareSources}
          isPipWindow={isPipWindow}
          apiToken={apiToken}
          meetingEndedPageUrl={meetingEndedPageUrl}
          meetingLeftPageUrl={meetingLeftPageUrl}
          participantRemovedPageUrl={participantRemovedPageUrl}
          websiteBaseUrl={websiteBaseUrl}
          eventHandler={eventHandler}
          onClose={onClose}
          inviteurl={inviteUrl}
          devMode={devMode}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
        />
      </LiveKitRoom>
    )
  );
}

export default DaakiaVC;
