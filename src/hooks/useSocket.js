import { useEffect, useRef, useState } from "react";
import { io } from "socket.io-client";
import { datadogLogs } from "@datadog/browser-logs";

const useSocket = ({ keepConnected, token, connectionUrl,user }) => {
  
  const socketRef = useRef(null);
  const [isSocketConnected, setIsSocketConnected] = useState(false); // Tracks connection state

  useEffect(() => {
    if (keepConnected && token && connectionUrl) {
      // Initialize socket connection
      socketRef.current = io(connectionUrl, {
        transports: ["websocket"],
        auth: { token },
      });

      socketRef.current.on("connect", () => {
        datadogLogs.logger.info("Socket connected",{
          user
        })
        setIsSocketConnected(true);
      });

      socketRef.current.on("disconnect", () => {
        setIsSocketConnected(false);
      });

      socketRef.current.on("connect_error", (err) => {
        setIsSocketConnected(false);
        datadogLogs.logger.error("Error in Socket while connection", {
          error: err,
          user
        });
      });
    }else if(!keepConnected && socketRef.current){
      socketRef.current.disconnect();
      socketRef.current = null; // Reset reference
      setIsSocketConnected(false);
    }

    // Cleanup: Disconnect and reset socket reference
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null; // Reset reference
        setIsSocketConnected(false);
      }
    };
  }, [keepConnected, token, connectionUrl]);
  return { socket: socketRef.current, isSocketConnected }; // Return socket and connection state
};

export default useSocket;
