/* eslint-disable no-underscore-dangle */

import { useState, useEffect } from "react";

export function useTroubleShoot(room) {
  const [localCandidateID, setLocalCandidateID] = useState(null);
  const [remoteCandidateID, setRemoteCandidateID] = useState(null);
  const [roundTripTimes, setRoundTripTimes] = useState([]);

  useEffect(() => {
    if (!room) return;
    const fetchRTCStats = async () => {
      const pubRTCStats =
        await room.localParticipant.engine.pcManager?.publisher?._pc?.getStats();

      if (pubRTCStats) {
        const newWebRTClog = { publisher: {} };

        pubRTCStats.forEach((rtcStat) => {
          if (
            rtcStat.type === "remote-candidate" &&
            rtcStat.id === remoteCandidateID
          ) {
            newWebRTClog.publisher.remoteCandidate = rtcStat;
          }

          if (
            rtcStat.type === "local-candidate" &&
            rtcStat.id === localCandidateID
          ) {
            newWebRTClog.publisher.localCandidate = rtcStat;
          }

          if (
            rtcStat.type === "candidate-pair" &&
            rtcStat.nominated &&
            rtcStat.state === "succeeded"
          ) {
            setLocalCandidateID(rtcStat.localCandidateId);
            setRemoteCandidateID(rtcStat.remoteCandidateId);
            newWebRTClog.publisher.availableOutgoingBitrate =
              rtcStat.availableOutgoingBitrate;
            newWebRTClog.publisher.currentRoundTripTime =
              rtcStat.currentRoundTripTime;

            setRoundTripTimes((prevTimes) => [
                ...prevTimes,
                { x: Date.now(), y: Math.round(rtcStat.currentRoundTripTime * 100) },
              ]);
          }
        });


      }
    };

    fetchRTCStats(); 

    const intervalId = setInterval(fetchRTCStats, 15000); 

    return () => clearInterval(intervalId); 
  }, [room, localCandidateID, remoteCandidateID]);
  return roundTripTimes;
}
