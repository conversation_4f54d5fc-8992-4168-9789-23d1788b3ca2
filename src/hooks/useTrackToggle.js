import  { useRef, useMemo, useEffect, useCallback } from "react";
import {
  setupMediaToggle,
  setupManualToggle,
  // log,
  //   useObservableState,
} from "@livekit/components-core";
import {
  useMaybeRoomContext,
  //   useObservableState,
} from "@livekit/components-react";
// import { useObservableState } from "@livekit/components-styles";
import { mergeProps } from "../utils/mergeProps";
import { useObservableState } from "./useObservableState";

/**
 * The `useTrackToggle` hook is used to implement the `TrackToggle` component and returns state
 * and functionality of the given track.
 *
 * @example
 * ```tsx
 * const { buttonProps, enabled } = useTrackToggle(trackRef);
 * return <button {...buttonProps}>{enabled ? 'disable' : 'enable'}</button>;
 * ```
 * @public
 */
export function useTrackToggle({
  source,
  onChange,
  initialState,
  captureOptions,
  publishOptions,
  onDeviceError,
  ...rest
}) {
  const room = useMaybeRoomContext();
  const track = room?.localParticipant?.getTrackPublication(source);
  /** `true` if a user interaction such as a click on the TrackToggle button has occurred. */
  const userInteractionRef = useRef(false);

  const { toggle, className, pendingObserver, enabledObserver } = useMemo(
    () =>
      room
        ? setupMediaToggle(
            source,
            room,
            captureOptions,
            publishOptions,
            onDeviceError
          )
        : setupManualToggle(),
    [room, source, JSON.stringify(captureOptions), publishOptions]
  );

  const pending = useObservableState(pendingObserver, false);
  const enabled = useObservableState(
    enabledObserver,
    initialState ?? !!track?.isEnabled
  );

  useEffect(() => {
    onChange?.(enabled, userInteractionRef.current);
    userInteractionRef.current = false;
  }, [enabled, onChange]);

  useEffect(() => {
    if (initialState !== undefined) {
      // log.debug("forcing initial toggle state", source, initialState);
      toggle(initialState);
    }
    // only execute once at the beginning
    
  }, []);

  const newProps = useMemo(
    () => mergeProps(rest, { className }),
    [rest, className]
  );

  const clickHandler = useCallback(
    (evt) => {
      userInteractionRef.current = true;
      toggle().finally(() => (userInteractionRef.current = false));
      rest.onClick?.(evt);
    },
    [rest, toggle]
  );

  return {
    toggle,
    enabled,
    pending,
    track,
    buttonProps: {
      ...newProps,
      "aria-pressed": enabled,
      "data-lk-source": source,
      "data-lk-enabled": enabled,
      disabled: pending,
      onClick: clickHandler,
    },
  };
}
