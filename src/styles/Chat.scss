.input-area {
  position: relative;
}

.text-area {
  background-color: #354657 !important;
  border: 1px solid #798593 !important;
  color: white;
}

.send-btn {
  // position: absolute;
  bottom: 15px;
  right: 10px;
  background-color: transparent !important;
  height: fit-content;
  width: fit-content;
  padding: 10px;
}

::placeholder {
  color: white !important;
}

::-webkit-scrollbar {
  display: none; /* Hide the scrollbar */
}

.lk-chat-d{
  position: relative;
  height: 98%;
  margin: 14px 8px 13px 0;
  animation: side-drawer 0.2s ease-in;
  width: 28rem;
  .ant-tabs-nav-wrap{
    .ant-tabs-nav-list{
      width: 100%;
      .ant-tabs-tab{
        color: white;
        font-size: 18px;
      }
    }
  }
  .lk-list{
    width: 100%;
  }
  .ant-tabs{
    width: 100%;
    height: 100%;
    &-content{
      height: 100%;
      .ant-tabs-tabpane{
        height: 100%;
        position: relative;
        width: 100%;
        .lk-chat{
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          .lk-chat-entry {
            .lk-message-outer{
              padding: 0 1rem;
              display: flex;
              flex-direction: column;
              // width: fit-content;
              .lk-timestamp{
                color: #BFBFBF;
                font-size: 9px;
              }
            }
          }
          .lk-chat-messages{
            // height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            &::-webkit-scrollbar{
              display: block;
              width: 0.5rem;
            }
            &::-webkit-scrollbar-thumb{
              background-color: #798593;
              border-radius: 0.25rem;
            }
            &::-webkit-scrollbar-track{
              background-color: #000;
            }
            .lk-message-body{
              border-radius: 0px 6px 6px 6px !important;
              color: white;
              background-color: #4a5767;
            }
            .lk-local{
              .lk-message-body{
                background-color: #131b26;
                border-radius: 6px 0px 6px 6px !important;
                display: flex;
              }
              .lk-message-outer{
                display: flex;
                width: auto;
                justify-content: flex-end;
                padding: 0 1rem;
                align-items: flex-end;
              }
              .lk-meta-data{
                flex-direction: row-reverse;
                .lk-timestamp{
                  margin: 0;
                }
                &-name{
                  display: flex;
                  flex-direction: row-reverse;
                  gap: 0.5rem;
                }
              }
            }
            .lk-meta-data{
              align-items: center;
              gap: 0.5rem;
              justify-content: space-between;
              .lk-timestamp{
                margin: 0;
              }
              &-name{
                display: flex;
                gap: 0.5rem;
              }
              .lk-participant-name{
                margin: 0;
              }
            }
          }
          .lk-chat-form{
            width: 100%;
          }
        }
      }
    }
  }
}
.lk-chat-form{
  &-outer{
    display: flex;
    align-items: center;
    width: 100%;
    background-color: #354657 !important;
    border: 1px solid #798593 !important;
    border-radius: 6px !important;
    .lk-chat-form-input{
      // padding: 0.625rem 0.5rem;
    }
  }
  &-input{
    width: 14.5rem;
    height: 2.65rem;
    border: none !important;
  }
  &-emoji{
    svg{
      width: 28px;
      height: 28px;
      margin-top: 10px;
    }
    cursor: pointer;
  }
  &-buttons{
    display: flex;
    align-items: center;
  }
  &-upload{
    font-size: 1.5rem;
    margin-right: 0.1rem;
    cursor: pointer;
  }
  &-divider{
    font-size: 1.3rem;
    font-weight: 300;
  }
}