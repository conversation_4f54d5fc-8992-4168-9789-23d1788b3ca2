@use "./variables" as *;

.vg-drawer .ant-drawer-wrapper-body .ant-drawer-body::-webkit-scrollbar {
  width: 0;
}

.vg-category-container {
  margin: 16px 0;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(217, 217, 217, 0.42);
  background-color: black;
  .vg-heading {
    text-align: left;
    font: 16px;
    font-weight: 600;
    font-family: $font;
    margin-bottom: 16px;
    color: white;
    display: flex;
    justify-content: space-between;
  }
}
.vg-card {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* This makes the div square */
  // overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
  &:hover {
    .delete-bg {
      display: flex;
    }
  }
  .delete-bg {
    position: absolute;
    top: -0.7rem;
    left: 2.8vw;
    border-radius: 50%;
    background-color: white;
    color: black;
    height: 1.4rem;
    width: 1.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    display: none;
    transition: all 0.3s;

    @media screen and (max-width: 992px) {
      left: 3.1rem;
    }
    @media screen and (max-width: 768px) {
      left: 10vw;
    }
    @media screen and (max-width: 576px) {
      left: 28vw;
    }
  }
  .vg-card-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensures the image covers the entire card */
    border-radius: 8px;

    svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover; /* Ensures the image covers the entire card */
      border-radius: 8px;
    }
  }
}
