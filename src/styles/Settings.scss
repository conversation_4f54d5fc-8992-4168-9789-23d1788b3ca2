.record-popover{
  padding: 0;
  .ant-popover-inner-content{
    width: 100%;
  }
}
.setting-control-button-popover{
  .ant-popover-inner-content{
    border-radius: 10px;
  }
  .ant-popover-inner{
    border-radius: 10px;
  }
}
// .settings-icon-clicked {
//   color: #0a84ff;
//   background-color: rgba(10, 132, 255, 0.14) !important;
// }
// .record-collapse .ant-collapse-content-active .settings-menu-inner-text{
//   display: flex;
//   margin-left: 2rem;
// }
// .record-items{
//   background-color: #2a2a2a;
//   border-radius: 6px;
// }
.settings-menu-item {
  color: white;
  padding: 2% 4%;
  display: flex;
  justify-content: start;
  cursor: pointer;
  // height: 3em;
  margin: 5px;
  gap: 0.7em;
  &:hover {
    background-color: #eff1f4;
    color: #000;
    border-radius: 6px;
    .settings-menu-inner-icon{
      svg{
        color: #000;
        filter: saturate(0%) hue-rotate(200deg) brightness(50%) contrast(90%);
      }
    }
  }
  .settings-menu-inner-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 20px;
    svg {
      color: black;
      width: 23px;
      height: 23px;
    }
  }
  .settings-menu-inner-text {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 1rem;
    width: auto;
  }
}
.setting-control-button{
  width: 11rem;
}

/* Media Queries for Responsiveness */
@media (max-width: 875px) {
  .settings-menu-item {
    padding: 2% 3%;
    gap: 0.6em;
    height: 2.8em;
  }

  .settings-menu-inner-text {
    font-size: 0.8rem;
  }

  .settings-menu-inner-icon svg {
    width: 20px;
    height: 20px;
  }
}


@media (max-width: 710px) {
  .settings-menu-item {
    padding: 2% 3%;
    gap: 0.5em;
    height: 2.5em;
  }

  .settings-menu-inner-text {
    font-size: 0.7rem;
  }

  .settings-menu-inner-icon svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 450px){
  .setting-control-button-popover{
    z-index: 10;
  }
  .setting-control-button{
    width: auto;
    .mvt-options{
      margin: 0;
      padding: 5px 10px;
    }
  }
}

.setting-control-button-mobile{
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  gap: 0.5rem;
}