@mixin title{
  text-align: center;
  font-weight: bold;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  color: white;
}
.nt-modal {
  .ant-modal-content{
    border-radius: 15px;
  }
  .ant-modal-body{
    border-radius: 12px;
    border: 1px solid #C8CCD2;
  }
  .ant-modal-close-icon{
    color: white;
  }
  .nt-parent-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    .nt-message-title {
      margin: 1%;
      font-size: 0.7rem;
      color: white;
    }
    .nt-message-content {
      text-align: center;
      font-weight: bold;
      margin: 2%;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
        "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        sans-serif;
      font-size: 0.9rem;
      color: white;
    }

    .nt-title {
      margin: 5% 0;
      margin-bottom: 7%;
      font-size: 1.5rem;
      @include title;
    }
    .nt-button-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: auto;
      width: 100%; /* Ensure the container takes full width */
      padding-right: 2%;
      gap: 2rem;
      .nt-button-cancel{
        border-color: white !important;
        color: white !important;
        border-radius: 5px;
      }
    }
  }

}