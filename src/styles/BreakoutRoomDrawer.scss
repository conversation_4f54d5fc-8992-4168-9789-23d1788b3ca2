@mixin rooms-layout {
  .ant-collapse-item {
    border: none;

    .ant-collapse-header {
      color: white;
    }

    .ant-collapse-content {
      background-color: transparent;

      .ant-list-empty-text{
        display: none;
      }
      .ant-collapse-content-box {
        padding: 0.3rem 1rem;
        max-height: 8.8rem;
        height: auto;
        overflow-y: auto;
        overflow-x: hidden;

        .br-user-info {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.3rem 0;
        }
      }
    }
  }

  .ant-collapse-item-active {
    .ant-collapse-header {
      padding: 0.3rem 0.6rem;
    }
  }
}

.br-body {
  width: 100%;

  &-add-room {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;

    button {
      background-color: transparent;
      color: white;
      border-radius: 8px;
      padding: 0 0.6rem;
      &:focus{
        background-color: #000;
      }
      &:hover{
        background-color: #000;
      }
    }
  }

  &-rooms {
    display: flex;
    flex-direction: column;
    margin-top: 1rem;
    gap: 1rem;

    &-main-room {
      background-color: transparent;
      border: none;
      @include rooms-layout;
    }

    &-room {
      display: flex;
      flex-direction: column;
      .ant-list-items{
        display: flex;
        flex-direction: column;
        // gap: 0.4rem;
      }
      .ant-collapse-content-box{
        max-height: 8.8rem;
        &::-webkit-scrollbar{
          width: 5px;
        }
        &::-webkit-scrollbar-thumb{
          background-color: #3c3c3c;
        }
        &::-webkit-scrollbar-track{
          background-color: #111111;
        }
      }
      &-panel-header{
        display: flex;
        justify-content: space-between;
        &-icons{
          display: flex;
          gap: 0.1rem;
          img{
            width: 20px;
          }
          &-add-participant{
            // padding: 0;
            .ant-popover-arrow{
              display: none;
            }
            .ant-popover-inner{
              background-color: #000;
              border-radius: 10px;
              border: 1px solid white;
              .ant-popover-title{
                color: white;
              }
              .ant-popover-inner-content{
                width: 100%;
                height: auto;
                .ant-list-items{
                  display: flex;
                  flex-direction: column;
                  margin-bottom: 1rem;
                  // max-height: 10rem;
                  height: 12rem;
                  gap: 0.3rem;
                  overflow-y: auto;
                  overflow-x: hidden;
                  
                  .ant-list-item{
                    border: none;
                    padding: 0;
                    .ant-checkbox-wrapper{
                      &::after{
                        display: none !important;
                      }
                      display: flex;
                      flex-direction: row-reverse;
                      align-items: center;
                      justify-content: space-between;
                      width: 100%;
                      .ant-checkbox{
                        margin-bottom: 0.3rem;
                      }
                      .br-user-info{
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                      }
                    }
                  }
                }
                .ant-btn{
                  display: flex;
                  margin: 0 auto;
                }
              }
            }
          }
        }
      }
      .ant-collapse {
        background-color: transparent;
        border: none;
        width: auto;
        margin-left: 0.8rem;
        &:first-child{
          margin: 0;
        }
        // margin-left: 0.8rem;
        @include rooms-layout;
        .ant-collapse-header-text{
          input{
            width: 18vw;
            border: none;
            padding: 0;
          }
        }
        .br-user {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .user-options{
            display: flex;
            gap: 0.2rem;
            img{
              cursor: pointer;
              width: 16px;
              &:nth-child(2){
                width: 20px;
              }
            }
          }
        }
      }
    }
  }

  &-suggestion {
    margin-top: 1rem;

    p {
      text-align: center;
    }
  }

}
.srp{
  width: auto;
  padding: 0;
  .ant-popover-content{
    width: auto;
  }
  .ant-popover-arrow{
    display: none;
  }
  .ant-popover-inner{
    border-radius: 10px;
    background-color: #242424 !important;
    width: auto;
    .ant-popover-title{
      color: white;
    }
    .ant-popover-inner-content{
      width: 100%;
      padding: 0;
      text-align: center;
      padding-bottom: 1rem;
      .br-switchto-main-room{
        display: flex;
        align-items: center;
        justify-content: center;
        color: white !important;
        padding: 12px 0;
        color: rgba(0, 0, 0, .85);
        cursor: pointer;
        &:hover{
          background-color: #3c3c3c;
        }
      }
      .ant-list-item{
        display: flex;
        align-items: center;
        justify-content: start;
        color: white !important;
        padding: 12px 0;
        color: rgba(0, 0, 0, .85);
        cursor: pointer;
        &:hover{
          background-color: #3c3c3c;
        }
      }
    }
  }
}