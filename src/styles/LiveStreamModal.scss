.ls-modal{
  .ant-modal-content{
    border-radius: 12px;
  }
  .ant-modal-body {
    border-radius: 10px;
    border: 1px solid #C8CCD2;
  }
  .ant-modal-close-icon{
    color: white;
  }
  .ls-container-parent{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    .ls-title {
      text-align: center;
      font-weight: bold;
      margin: 10px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
        "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        sans-serif;
      font-size: 1.5rem;
    }
    .ls-description {
      font-size: 1rem;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
        "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        sans-serif;
      color: white;
      margin-top: 1rem;
    }
    .ls-button-container {
      display: flex;
      justify-content: flex-end;
      width: 100%; /* Ensure the container takes full width */
      padding-right: 2%;
      gap: 10px;
      .ls-button {
        border-radius: 5px;
      }
      .ls-button-cancel {
        border-color: white !important;
        color: white !important;
        border-radius: 5px;
      }
    }
  }
  .ls-form-input-container {
    width: 100%;
    margin-bottom: 20px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
      "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
      sans-serif;
    font-size: 1rem;
    .ls-input {
      background-color: #1c1c1e;
      color: white;
    }
  }
}








