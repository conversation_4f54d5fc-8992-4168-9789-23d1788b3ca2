.rtp-success{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;

  &-message{
    text-align: center;
    margin-bottom: 5rem;
  }
}
.rtp-heading {
  color: #0a84ff;
  font-family: Segoe UI;
  font-size: 1rem;
  font-weight: 600;
  line-height: 26.6px;
  text-align: left;
  margin-bottom: 0.5rem;
}
.rtp-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-left: 1.2rem;
  .ant-checkbox-wrapper {
    margin-left: 0 !important;
    color: white;
  }
  p {
    font-family: Segoe UI;
    font-size: 16px;
    font-weight: 400;
    line-height: 26.6px;
    text-align: left;
    margin-bottom: 0;
  }
  span {
    font-family: Segoe UI;
    font-size: 12px;
    font-weight: 400;
    line-height: 18.62px;
    text-align: left;
  }
}
.rtp-hr {
  border: 1px solid white;
  margin: 0.5rem 0 !important;
  margin-bottom: 1.2rem !important;
}
.rtp-other-issues {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  p {
    font-family: Segoe UI;
    font-size: 14px;
    font-weight: 400;
    line-height: 21.28px;
    text-align: left;
    color: white;
    margin-bottom: 0;
  }
  .rtp-input-box {
    position: relative;
    width: 100%;
    margin-top: 1rem;
    textarea {
      min-height: 120px;
      background-color: transparent;
      color: white;
      font-size: 14px;
      border-radius: 10px;
      resize: none;
      position: relative;
      p {
        position: absolute;
        right: 0;
        bottom: 0;
        color: white;
        font-size: 12px;
      }
    }
    p {
      color: white;
      position: absolute;
      right: 6px;
      bottom: 0;
    }
  }
  .rtp-buttons {
    display: flex;
    justify-content: space-around;
    width: 100%;
    
    button{
      border-radius: 6px;
    }
    button:nth-child(2) {
      background-color: #0a84ff;
    }
  }
}


@media screen and (max-height: 570px) {
  .rtp-heading{
    font-size: 16px;
  }
  .rtp-checkboxes p{
    font-size: 14px;
  }
  .rtp-other-issues p{
    font-size: 12px;
  }
  .rtp-input-box textarea{
    min-height: 100px;
    font-size: 12px;
  }
  .rtp-buttons{
    .ant-btn{
      padding: 3px 10px;
      >span{
        font-size: 14px;
      }
    }
  } 
}