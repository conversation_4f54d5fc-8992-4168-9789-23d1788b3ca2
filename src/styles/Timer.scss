.timer-popover{
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(0,0,0,0.2);
    .ant-popover-content{
        border-radius: 8px;
    }
    .ant-popover-inner{
        border-radius: 8px;
        &-content{
            // border: 1px solid white;
            border-radius: 6px;
            background-color: #1e1e1e;
        }
    }
    &-content{
        display: flex;
        &-time{
            display: flex;
            flex-direction: column;
            color: white;
            justify-content: center;
            // align-items: center;
            gap: 0.5rem;
            font-size: 14px;
            div{
                display: flex;
                flex-direction: column;
                // justify-content: center;
                // align-items: center;
                p{
                    margin: 0;
                    font-weight: 600;
                    span{
                        font-weight: normal;
                    }
                }
            }
            &:first-child{
                border-right: 1px solid white;
                padding-right: 1rem;
            }
            &:last-child{
                padding-left: 1rem;
            }
        }
    }
}