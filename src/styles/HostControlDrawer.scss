.hc {
  display: flex;
  flex-direction: column;
  padding: 4px;
  &-subhead {
    font-family: Segoe UI;
    font-size: 14px;
    font-weight: 400;
    line-height: 18.62px;
    text-align: left;
  }
  &-component {
    display: flex;
    flex-direction: column;
    &-switch {
      display: flex;
      width: 100%;
      justify-content: space-between;
      p {
        font-family: Segoe UI;
        font-size: 20px;
        font-weight: 600;
        line-height: 26.6px;
        text-align: left;
      }
      .ant-switch{
        background-color: #d9d9d9;
        &.ant-switch-checked{
          background-color: #30CA9F;
        }
      }
    }
    > p {
      font-family: Segoe UI;
      font-size: 14px;
      font-weight: 400;
      line-height: 18.62px;
      text-align: left;
    }
  }
  hr{
    opacity: 1;
  }
  .component-2{
    margin-top: 1.2rem;
  }
}
