.tsh {
  position: relative;
  &-modal{
    position: relative;
    height: 100%;
    width: 645px !important;
    height: auto;
    overflow-x: hidden;
    padding: 0;
    scrollbar-width: none;
    position: sticky;
    background-color: white;
    top: 4rem;
  }
  .ant-modal-wrap {
    ::-webkit-scrollbar {
      width: 0;
    }
  }

  .tsh-modal-header {
    // position: fixed;
    width: 100%;
    display: flex;
    flex-direction: column;
    .troubleshoot-meeting-header {
      // position: fixed;
      // top: 5rem;
      font-size: 1.2rem;
      font-weight: bold;
      background-color: white;
    }
    hr {
      border: 1px solid #9d9d9d;
      width: 100%;
      position: sticky;
      top: 3rem;
      margin-top: 0;
    }
  }

  .tsh-body-content {
    height: auto;
    position: sticky;
    top: 7rem;
    overflow-x: hidden;
    ::-webkit-scrollbar {
      width: 0;
    }
    .tsh-network-connections {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 2rem;
      &-nc {
        font-family: Segoe UI;
        font-size: 25px;
        font-weight: 700;
        line-height: 33.25px;
        text-align: left;
        display: flex;
        gap: 1rem;
        &-quality{
          color: white;
          display: flex;
          @mixin qualityStyles{
            padding: 4px 8px;
            font-weight: normal;
            font-size: 1.2rem;
            border-radius: 6px;
            width: 85px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 32px;
          }
          &-good{
            @include qualityStyles;
            background-color: green;
          }
          &-average{
            @include qualityStyles;
            background-color: yellow;
            color: black;
          }
          &-bad{
            @include qualityStyles;
            background-color: red;
          }
        }
      }
    }
    > div {
      > p {
        font-family: Segoe UI;
        font-size: 20px;
        font-weight: 400;
        line-height: 33.25px;
        text-align: left;
        color: #818088;
      }
      > ul {
        padding: 0;
        li {
          font-family: Segoe UI;
          font-size: 20px;
          font-weight: 600;
          line-height: 33.25px;
          text-align: left;
          color: #242424;
          list-style: none;
        }
      }
    }
    .apexcharts-title-text {
      font-family: Segoe UI;
      font-size: 18px;
      font-weight: 600;
      line-height: 29.38px;
      text-align: left;
      color: #818088;
    }
    .apexcharts-subtitle-text {
      font-family: Segoe UI;
      font-size: 12px;
      font-weight: 600;
      line-height: 33.25px;
      text-align: left;
      color: #242424;
    }
  }
}

.tsh-modal {
  
  .ant-modal-close {
    position: sticky;
    top: 0rem;
    left: 36.5rem;
  }
  .ant-modal-content,
  .ant-modal-body {
    height: 100%;
  }
  .ant-modal-body {
    background-color: #fff !important;
    color: black !important;
    align-items: flex-start !important;
    padding-top: 0;
    > p {
      font-family: Segoe UI;
      font-size: 20px;
      font-weight: 600;
      line-height: 26.6px;
      text-align: left;
    }
  }
}