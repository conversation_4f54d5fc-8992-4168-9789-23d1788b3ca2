import axios from "axios";
import momentTimezone from "moment-timezone";
import { getLocalStorageToken } from "../utils/helper";
import { constants } from "../utils/constants";

export const APIrequest = async ({
  method,
  endpoint,
  payload,
  token = null,
  devmode = false,
}) => {
  let apiToken = null;
  if (token !== null) {
    apiToken = token;
  } else {
    apiToken = getLocalStorageToken();
  }
  try {
    const axiosConfig = {
      method: method || "GET",
      baseURL: devmode ? constants.STAG_BASE_URL : constants.PROD_BASE_URL,
      headers: {
        "content-type": "application/json",
        "X-Frame-Options": "sameorigin",
        timezone: momentTimezone.tz.guess(true),
      },
    };

    if (apiToken) {
      axiosConfig.headers = {
        ...axiosConfig.headers,
        Authorization: apiToken,
      };
    }
    if (endpoint) {
      axiosConfig.url = endpoint;
    }

    if (payload) {
      const bodyPayload = {};
      for (const key in payload) {
        if (Object.hasOwnProperty.call(payload, key)) {
          let element = payload[key];
          if (typeof element === "string") {
            element = element.trim();
          }
          if (![null, undefined, NaN].includes(element)) {
            bodyPayload[key] = element;
          }
        }
      }
      axiosConfig.data = bodyPayload;
    }

    const response = await axios(axiosConfig);

    if (response.status >= 400) {
      throw new Error(response.message);
    }

    return response.data;
  } catch (error) {
    if(error?.response?.status === 400 &&
      error?.response?.data !== undefined &&
      error?.response?.data?.message !== undefined
    ) {
      console.log("Error: ",error.response);
      throw new Error(error.response.data.message);
    } else {
      console.log(error);
      throw error;
    }
  }
};
