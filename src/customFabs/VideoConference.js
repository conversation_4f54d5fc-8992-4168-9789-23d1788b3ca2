/* eslint-disable */
import React, { useEffect, useState, useRef, useCallback } from "react";
import "../styles/VideoConference.scss";
import {
  isEqualTrackRef,
  isTrackReference,
  isWeb,
  isMobileBrowser,
  log,
} from "@livekit/components-core";
import {
  RoomEvent,
  Track,
  DisconnectReason,
  ConnectionState,
} from "livekit-client";
import { Button } from "antd";
import {
  CarouselLayout,
  // ConnectionStateToast,
  FocusLayoutContainer,
  GridLayout,
  LayoutContextProvider,
  RoomAudioRenderer,
  // Chat,
  usePinnedTracks,
  useTracks,
  useCreateLayoutContext,
  // formatChatMessageLinks,
  TrackRefContext,
  Toast,
} from "@livekit/components-react";

import moment from "moment";
import { ReactComponent as DaakiaLogo } from "./icons/DaakiaLogoLight.svg";
import {
  DataReceivedEvent,
  sounds,
  DISCONNECT_REASON,
  constants,
  SocketChannel,
  DrawerState,
} from "../utils/constants";
import { formatChatMessageLinks } from "../components/chats/ChatsEntry";
import useSocket from "../hooks/useSocket";

import { ControlBar } from "./ControlBar";
import { ParticipantTile } from "./ParticipantTile";

import { MeetindEndedModal } from "../components/settings/MeetindEndingModal";
import { ParticipantList } from "../components/participants/ParticipantList";
import { NotificationModal } from "../components/NotificationModal";
import { VirtualBackgroundDrawer } from "../components/settings/VirtualBackgroundDrawer";
import { ReportProblemDrawer } from "../components/settings/ReportProblemDrawer";
import { RecordingConsentModal } from "../components/RecrodingConsent/RecordingConsentModal";
import BreakoutRoomDrawer from "../components/participants/BreakoutRoomDrawer";
import AllowParticipant from "../components/AllowParticipant";

import HostControlDrawer from "../components/settings/HostControlDrawer";
import LiveCaptionsDrawer from "../components/LiveCaptions/LiveCaptionsDrawer";
import { BreakoutRoomService } from "../services/BreakoutRoomServices";
import { getLocalStorage, removeLocalStorageItem, setLocalStorage,generateAvatar } from "../utils/helper";
import { VideoConferenceService } from "../services/VideoConferenceServices";
import Whiteboard from "../components/whiteboard/Whiteboard";
import ChatsDrawer from "../components/chats/ChatsDrawer";

import { RecordingConsentDrawer } from "../components/RecrodingConsent/RecordingConsentDrawer";
import { ConnectionStateToast } from "./ConnectionStateToast";
import { WhiteboardService } from "../services/WhiteboardServices";
import isElectron from "is-electron";
import PipLayout from "./PipLayout";
import { SettingsMenuServices } from "../services/SettingsMenuServices";
import StatusNotification from "../components/StatusNotification/StatusNotification";

export function VideoConference({
  room,
  setMovingRoomToken,
  setIsMovingToRoom,
  SettingsComponent,
  isMovingToRoom,
  meetingFeatures,
  isWebinarMode,
  setIsUsingBreakoutRoom,
  isElectronApp,
  screenShareSources,
  isPipWindow,
  participantRemovedPageUrl,
  meetingLeftPageUrl,
  meetingEndedPageUrl,
  apiToken,
  websiteBaseUrl,
  eventHandler,
  onClose,
  inviteurl,
  devMode,
  isSelfVideoMirrored, // currently not being used
  setIsSelfVideoMirrored,
  ...props
}) {
  const [refresh, setRefresh] = useState(0);
  const [connected, setConnected] = useState(false);
  const [connectionState, setConnectionState] = useState(null);
  const [isDisconneted, setIsDisconnected] = useState(false);
  const [widgetState, setWidgetState] = React.useState({
    showChat: false,
    unreadMessages: 0,
    showSettings: false,
  });
  const [showParticipantsList, setShowParticipantsList] = useState(false);
  const [showHostControl, setShowHostControl] = useState(false);
  const [showBreakoutRoom, setShowBreakoutRoom] = useState(false);
  const [showRaiseHand, setShowRaiseHand] = useState(false);
  const [showEmojiReaction, setShowEmojiReaction] = useState(null);
  const [remoteRaisedHands, setRemoteRaisedHands] = useState(new Map());
  const [notificationVisible, setNotificationVisible] = useState(false);
  const [notificationContent, setNotificationContent] = useState({});
  const [notificationAction, setNotificationAction] = useState("");
  const [remoteEmojiReactions, setRemoteEmojiReactions] = useState(new Map());
  const [showRecording, setShowRecording] = useState(
    props.meetingDetails?.is_recording_active || false
  );
  const [isVBDrawerOpen, setIsVBDrawerOpen] = useState(false);
  const [isRPDrawerOpen, setIsRPDrawerOpen] = useState(false);
  const [isLiveCaptionsDrawerOpen, setIsLiveCaptionsDrawerOpen] =
    useState(false);
  const [toastNotification, setToastNotification] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastStatus, setToastStatus] = useState("");
  const [isCoHost, setIsCoHost] = useState(false);
  const [isForceMuteAll, setIsForceMuteAll] = useState(false);
  const [isForceVideoOffAll, setIsForceVideoOffAll] = useState(false);
  const [forceMute, setForceMute] = useState(false);
  const [forceVideoOff, setForceVideoOff] = useState(false);
  const [lobbyParticipants, setLobbyParticipants] = useState(new Map()); // eslint-disable-line no-unused-vars
  const [coHostToken, setCoHostToken] = useState(null);
  const [drawerState, setDrawerState] = useState(null);
  const [breakoutRooms, setBreakoutRooms] = useState({});
  const [roomKeyCounter, setRoomKeyCounter] = useState(1);
  const [currentRoomName, setCurrentRoomName] = useState("");
  const [isBreakoutRoom, setIsBreakoutRoom] = useState(false);
  const [breakoutRoomDuration, setBreakoutRoomDuration] = useState(5);
  const [endBreakoutRoomTimer, setEndBreakoutRoomTimer] = useState(
    breakoutRoomDuration * 60
  );
  const [isBreakoutRoomCnfigSet, setIsBreakoutRoomCnfigSet] = useState(false);
  const lastAutoFocusedScreenShareTrack = React.useRef(null);
  const [liveCaptionData, setLiveCaptionData] = useState(null);
  const [privatechatparticipants, setPrivateChatParticipants] = useState([]);
  const [selectedPrivateChatParticipant, setSelectedPrivateChatParticipant] =
    useState(null);
  const [showPrivateChat, setShowPrivateChat] = useState(false);
  const [privatechatmessages, setPrivateChatMessages] = useState(new Map());
  const [newMessageRender, setNewMessageRender] = useState(0);
  const [publicChatUnreadMessagesCount, setPublicChatUnreadMessagesCount] =
    useState(0);
  const [privateChatUnreadMessagesCount, setPrivateChatUnreadMessagesCount] =
    useState(0);
  const [liveCaptionsObject, setLiveCaptionsObject] = useState({
    showIcon:
      props.meetingDetails?.transcription_detail?.transcription_enable ||
      props.isHost ||
      isCoHost,
    isLanguageSelected:
      props.meetingDetails?.transcription_detail?.transcription_enable || false,
    langCode:
      props.meetingDetails?.transcription_detail?.transcription_lang_iso ||
      "en-IN",
  });
  const [finalCaptions, setFinalCaptions] = useState([]);
  const [isPIPEnabled, setIsPIPEnabled] = useState(null);
  const [canDownloadChatAttachment, setCanDownloadChatAttachment] =
    useState(false);
  const [sipData, setSipData] = useState(null);
  const [isWhiteboardOpen, setIsWhiteboardOpen] = useState(false);
  const [whiteboardData, setWhiteboardData] = useState({
    elements: [],
    appState: {},
  });
  const [allowLiveCollabWhiteBoard, setAllowLiveCollabWhiteBoard] =
    useState(false);
  const [publicChatMessages, setPublicChatMessages] = useState([]);
  const [isExitWhiteboardModalOpen, setIsExitWhiteboardModalOpen] =
    useState(false);
  const [whiteboardSceneData, setWhiteboardSceneData] = useState({
    start: 0,
    end: 0,
  });
  const [whiteboardId, setWhiteboardId] = useState(null);
  const [isPinned, setIsPinned] = useState(false);
  const [screenShareDisplayId, setScreenShareDisplayId] = useState(1);
  const [translationDetails, setTranslationDetails] = useState({
    source_lang: undefined,
    target_lang: "",
    text_cap: "",
  });
  const [finalTranslatedCaptions, setFinalTranslatedCaptions] = useState([]);
  // check if screen share is enabled
  const [isScreenShareEnabled, setIsScreenShareEnabled] = useState(false);
  const [screenShareMode, setScreenShareMode] = useState("text");
  const [isFocusTrackEnabled, setIsFocusTrackEnabled] = useState(false);
  const [targetLangChangedFor, setTargetLangChangedFor] = useState(0);
  // auto meeting end time
  const [autoMeetingEndTime, setAutoMeetingEndTime] = useState(
    moment(props.meetingDetails?.meeting_config?.auto_meeting_end_schedule)
  );
  const [endMeetingNotificaton, setEndMeetingNotification] = useState(false);

  const [isRecordingLoading, setIsRecordingLoading] = useState(false);

    // Recording Consent
    const [isRecordingConsentModalOpen, setIsRecordingConsentModalOpen] = useState(false);
    const [participantConsent, setParticipantConsent] = useState([]);
    const [showRecordingConsentDrawer, setShowRecordingConsentDrawer] = useState(false);
    const [showRecordingConsentIcon, setShowRecordingConsentIcon] = useState(props.meetingDetails?.recording_consent_active === 1 || false);
    const [showDeniedModal, setShowDeniedModal] = useState(false);

  const dismissed = useRef(false); // Track if user dismissed it
  const hasShownToast = useRef(false); // Track if toast has been shown

  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { updateOnlyOn: [RoomEvent.ActiveSpeakersChanged], onlySubscribed: false }
  );

  const widgetUpdate = (state) => {
    log.debug("updating widget state", state);
    setWidgetState(state);
  };

  const onScreenShareChange = useCallback(
    (enabled) => {
      setIsScreenShareEnabled(enabled);
    },
    [setIsScreenShareEnabled]
  );

  const layoutContext = useCreateLayoutContext();

  const screenShareTracks = tracks
    .filter(isTrackReference)
    .filter((track) => track.publication.source === Track.Source.ScreenShare);

  const focusTrack = usePinnedTracks(layoutContext)?.[0];
  const carouselTracks = tracks.filter(
    (track) => !isEqualTrackRef(track, focusTrack)
  );

  // Socket connection

  const { socket, isSocketConnected } = useSocket({
    keepConnected: isWhiteboardOpen,
    token: props.token,
    connectionUrl: devMode
      ? constants.SOCKET_URL_DEV
      : constants.SOCKET_URL_PROD,
    user: room?.localParticipant?.participantInfo,
  });

  // Get breakout room details
  const fetchBreakoutRoomDetails = async () => {
    setTimeout(async () => {
      try {
        if (!room) return;
        const response = await BreakoutRoomService.getBreakoutRoomDetail(
          props.id,
          props.isHost ? apiToken : coHostToken,
          room?.localParticipant?.participantInfo,
          devMode
        );
        if (response?.success === 0) {
          console.log("Error getting breakout room details", response);
          return;
        }

        if (response?.data.length > 0) {
          setBreakoutRooms((prevRooms) => {
            const updatedRooms = { ...prevRooms };
            const allKeys = [];
            // Push all the keys of the existing rooms into allKeys
            // Push all the keys of the existing rooms into allKeys
            Object.keys(updatedRooms).forEach((key) => {
              if (updatedRooms[key].manual) {
                allKeys.push(key);
              }
            });
            response.data.forEach((r) => {
              const roomId = r?.name.split("__BR")[1];
              const key = roomId || "0";
              const roomName = roomId ? `Breakout Room ${key}` : "Main Room";
              const participants = r?.participants;

              allKeys.push(key);

              if (updatedRooms[key]) {
                // Update participants of the existing room
                updatedRooms[key].participants = participants;
                // delete updatedRooms[key].manual;
              } else {
                // Add new room
                updatedRooms[key] = {
                  name: roomName,
                  participants,
                  key,
                };
              }
            });

            for (let key in updatedRooms) {
              if (
                !allKeys.includes(key) && // Key not in allKeys array
                (updatedRooms[key].isDeleted ||
                  !updatedRooms[key].manual || // Manual is false
                  !("manual" in updatedRooms[key])) && // or manual is not present
                key !== "0" // Key is not "0"
              ) {
                delete updatedRooms[key];
              }
            }

            const activeRooms = Object.keys(updatedRooms).filter(
              (key) =>
                !updatedRooms[key].isDeleted ||
                !("isDeleted" in updatedRooms[key])
            );
            if (activeRooms.length <= 1 && showBreakoutRoom) {
              setDrawerState(DrawerState.PARTICIPANTS);
              setIsBreakoutRoomCnfigSet(false);
            }
            // Ensure the main room is always present
            if (!updatedRooms["0"]) {
              updatedRooms["0"] = {
                name: "Main Room",
                participants: [],
                key: "0",
              };
            }
            // Get the maximum value from the allKeys array
            const maxKey = Math.max(...allKeys);

            setRoomKeyCounter(maxKey + 1);

            return updatedRooms;
          });
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.log("Error getting breakout room details", error);
      }
    }, 500);
  };

  // Poll breakout room details
  useEffect(() => {
    if (!room || room?.state !== "connected") return;
    // Define a function to start polling
    const startPolling = () => {
      // Set interval for polling every 15 seconds
      const intervalId = setInterval(fetchBreakoutRoomDetails, 10000);

      // Cleanup function to clear the interval
      return () => clearInterval(intervalId);
    };

    let stopPolling; // Variable to hold the cleanup function for polling

    if (
      room?.localParticipant?.metadata &&
      (JSON.parse(room?.localParticipant?.metadata)?.role_name ===
        "moderator" ||
        JSON.parse(room?.localParticipant?.metadata)?.role_name === "cohost") &&
      (showBreakoutRoom || drawerState === DrawerState.PARTICIPANTS) &&
      Object.keys(breakoutRooms).length > 1
    ) {
      stopPolling = startPolling(); // Start polling and keep the cleanup function
    }
    if (
      room?.localParticipant?.metadata &&
      JSON.parse(room?.localParticipant?.metadata)?.role_name === "moderator" &&
      Object.keys(breakoutRooms).length > 1
    ) {
      fetchBreakoutRoomDetails();
      room.on(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
      room.on(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
    }

    if (
      JSON.parse(room?.localParticipant?.metadata)?.role_name === "cohost" &&
      coHostToken &&
      coHostToken !== "" &&
      Object.keys(breakoutRooms).length > 1
    ) {
      fetchBreakoutRoomDetails();
      room.on(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
      room.on(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
    }

    // Cleanup on unmount or when dependencies change
    return () => {
      room?.off(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
      room?.off(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
      if (stopPolling) stopPolling(); // Call the cleanup function for polling
    };
  }, [
    room,
    room?.localParticipant?.metadata,
    showBreakoutRoom,
    coHostToken,
    drawerState,
    Object.keys(breakoutRooms).length,
  ]);

  // Handle Event for participant  disconnected
  useEffect(() => {
    if (!room) return;

    const onParticipantDisconnected = (participant) => {
      // Find the participant in the list
      const participantIndex = privatechatparticipants.findIndex((part) => {
        return (
          part.participant.identity.toString() ===
          participant.identity.toString()
        );
      });

      if (participantIndex >= 0) {
        // Create a new array with the updated participant's isConnected status
        const updatedParticipants = [...privatechatparticipants];
        updatedParticipants[participantIndex] = {
          ...updatedParticipants[participantIndex],
          isConnected: false,
        };

        // Update the state with the new array
        setPrivateChatParticipants(updatedParticipants);

        // Update selectedPrivateChatParticipant if it matches the disconnected participant
        if (
          selectedPrivateChatParticipant &&
          selectedPrivateChatParticipant.participant.identity.toString() ===
            participant.identity.toString()
        ) {
          // Create a new object for the selected participant
          const updatedSelectedParticipant = {
            ...selectedPrivateChatParticipant,
            isConnected: false,
          };

          setSelectedPrivateChatParticipant(updatedSelectedParticipant);
        }
      }
            // if showRecordingConsentIcon is true, then remove the participant from the participantConsent
            if(showRecordingConsentIcon){
              const participantIndex = participantConsent.findIndex((part) => {
                return part.participantId === participant.identity;
              });
              if(participantIndex >= 0){
                // remove the participant from the participantConsent
                const updatedParticipantConsent = participantConsent.filter((part) => {
                  return part.participantId !== participant.identity;
                });
                setParticipantConsent(updatedParticipantConsent);
              }
            }
    };

    room.on(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);

    return () => {
      room.off(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);
    };
  }, [room, privatechatparticipants, selectedPrivateChatParticipant,showRecordingConsentIcon,participantConsent]);

  // PIP mode functions
  const getRowCount = (length) => {
    return length > 2 ? 2 : length > 0 ? 1 : 0;
  };
  const getColCount = (length) => {
    return length < 2 ? 1 : length < 5 ? 2 : 3;
  };

  // Design configuration for PiP
  const pipDesign = {
    header: {
      height: 48,  // Increased header height
      backgroundColor: '#1a1a1a',
      textColor: '#ffffff',
      fontSize: 18,  // Increased font size
      showControls: true,
      controlsColor: '#ffffff',
      controlsSize: 24,  // Increased control size
    },
    background: {
      type: 'solid',
      color: '#2c2c2c',
    },
    border: {
      width: 2,
      color: '#3a3a3a',
      radius: 8,
    },
    logo: {
      show: false,
      position: 'bottomRight',
      text: 'Daakia',
      fontSize: 16,  // Increased font size
      fontColor: 'rgba(255, 255, 255, 0.6)',
    },
    videoSpacing: 6,  // Increased spacing
    contentPadding: 12,  // Increased padding
    speakingDelay: 1000, // Delay in ms before showing a new speaker
    maxSmallWindows: 2, // Maximum number of small windows to show
  };

  const pipWindowRef = useRef(null);
  const togglePipMode = async (enabled) => {
    // Check browser compatibility
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
  
    if (isSafari || isFirefox) {
      setToastNotification("Picture-in-Picture is not supported in your browser");
      setToastStatus("error");
      setShowToast(true);
      return;
    }
  
    if (pipWindowRef.current && document.pictureInPictureElement) {
      await document.exitPictureInPicture();
      pipWindowRef.current = null;
      return;
    }
    if (!enabled) return;
    if ("pictureInPictureEnabled" in document) {
      const source = document.createElement("canvas");
      // Increase canvas size for better visibility
      source.width = 640;  // Increased from 1920
      source.height = 360;  // Increased from 1080
      const ctx = source.getContext("2d", { alpha: false });
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
  
      const pipVideo = document.createElement("video");
      pipWindowRef.current = pipVideo;
      pipVideo.autoplay = true;
      pipVideo.playsInline = true;
      // Set video size to be larger
      pipVideo.style.width = '640px';  // Set explicit width
      pipVideo.style.height = '360px'; // Set explicit height
      pipVideo.style.minWidth = '640px'; // Ensure minimum size
      pipVideo.style.minHeight = '360px';
  
      const stream = source.captureStream(60);
      pipVideo.srcObject = stream;
      drawCanvas();
  
      // Add Media Session handlers
      if ("mediaSession" in navigator) {
        // Ensure tracks are published
        const ensureTracksPublished = async () => {
          // Ensure camera track is published
          if (!room.localParticipant.getTrackPublication(Track.Source.Camera)) {
            await room.localParticipant.setCameraEnabled(true);
            await room.localParticipant.setCameraEnabled(false);
          }
  
          // Ensure microphone track is published
          if (!room.localParticipant.getTrackPublication(Track.Source.Microphone)) {
            await room.localParticipant.setMicrophoneEnabled(true);
            await room.localParticipant.setMicrophoneEnabled(false);
          }
        };
  
        // Function to update media session metadata
        const updateMediaSessionMetadata = () => {
          const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
          const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
          const isCameraEnabled = cameraTrack ? !cameraTrack.isMuted : false;
          const isMicEnabled = micTrack ? !micTrack.isMuted : false;
  
          navigator.mediaSession.setCameraActive(isCameraEnabled);
          navigator.mediaSession.setMicrophoneActive(isMicEnabled);
  
          navigator.mediaSession.metadata = new MediaMetadata({
            title: "Daakia",
            artist: "Video Conference ",
            artwork: [
              {
                src: `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 96 96">
                  <rect width="96" height="96" fill="${isMicEnabled ? '#00ff00' : '#ff0000'}"/>
                  <text x="48" y="48" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dominant-baseline="middle">
                    ${isMicEnabled ? 'Mic On' : 'Mic Off'}
                  </text>
                </svg>`,
                sizes: "96x96",
                type: "image/svg+xml",
              }
            ]
          });
        };
  
        // Initialize tracks before setting up handlers
        ensureTracksPublished().then(() => {
          // Set initial states
          updateMediaSessionMetadata();
  
          // Add track state change listeners
          room.on(RoomEvent.TrackMuted, updateMediaSessionMetadata);
          room.on(RoomEvent.TrackUnmuted, updateMediaSessionMetadata);
  
          // Add action handlers
          navigator.mediaSession.setActionHandler("hangup", () => {
            if (isElectronApp) {
              window?.electronAPI?.ipcRenderer?.send("stop-annotation");
            }
            room.disconnect();
          });
  
          // Initialize camera handler with current state
          navigator.mediaSession.setActionHandler("togglecamera", () => {
            const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
            if (cameraTrack) {
              const isCurrentlyEnabled = !cameraTrack.isMuted;
              room.localParticipant.setCameraEnabled(!isCurrentlyEnabled);
            }
          });
  
          // Initialize microphone handler with current state
          navigator.mediaSession.setActionHandler("togglemicrophone", () => {
            const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
            if (micTrack) {
              const isCurrentlyEnabled = !micTrack.isMuted;
              room.localParticipant.setMicrophoneEnabled(!isCurrentlyEnabled);
            }
          });
        });
  
        // Clean up track state change listeners when leaving PiP
        pipVideo.addEventListener("leavepictureinpicture", (event) => {
          pipWindowRef.current = null;
          setIsPIPEnabled(false);
          pipVideo.srcObject.getTracks().forEach((track) => track.stop());
  
          // Clean up media session handlers and track listeners
          if ("mediaSession" in navigator) {
            navigator.mediaSession.setActionHandler("hangup", null);
            navigator.mediaSession.setActionHandler("togglecamera", null);
            navigator.mediaSession.setActionHandler("togglemicrophone", null);
            room.off(RoomEvent.TrackMuted, updateMediaSessionMetadata);
            room.off(RoomEvent.TrackUnmuted, updateMediaSessionMetadata);
          }
        });
      }
  
      pipVideo.onloadedmetadata = () => {
        pipVideo.requestPictureInPicture();
      };
      await pipVideo.play();
  
      pipVideo.addEventListener("enterpictureinpicture", (event) => {
        drawCanvas();
        setIsPIPEnabled(true);
      });
  
      pipVideo.addEventListener("leavepictureinpicture", (event) => {
        pipWindowRef.current = null;
        setIsPIPEnabled(false);
        pipVideo.srcObject.getTracks().forEach((track) => track.stop());
  
        // Clean up media session handlers when leaving PiP
        if ("mediaSession" in navigator) {
          navigator.mediaSession.setActionHandler("hangup", null);
          navigator.mediaSession.setActionHandler("togglecamera", null);
          navigator.mediaSession.setActionHandler("togglemicrophone", null);
        }
      });
  
      function drawCanvas() {
        const videos = document.querySelectorAll("video");
        try {
          // Only log if there are videos and they have changed
          if (videos.length > 0) {
            const currentVideoState = Array.from(videos).map(video => {
              const tracks = video.srcObject?.getVideoTracks() || [];
              return {
                trackLabel: tracks[0]?.label,
                trackKind: tracks[0]?.kind,
                trackSettings: tracks[0]?.getSettings(),
                trackConstraints: tracks[0]?.getConstraints(),
                trackEnabled: tracks[0]?.enabled,
                trackMuted: tracks[0]?.muted,
                trackReadyState: tracks[0]?.readyState,
                parentElement: video.parentElement?.className,
                videoElement: {
                  id: video.id,
                  className: video.className,
                  width: video.videoWidth,
                  height: video.videoHeight
                }
              };
            });
  
            // Compare with previous state
            const stateChanged = JSON.stringify(currentVideoState) !== JSON.stringify(window._lastVideoState);
            if (stateChanged) {
              window._lastVideoState = currentVideoState;
            }
          }

          drawCustomBackground(ctx, source.width, source.height);
  
          // Find screen share video
          const screenShareVideo = Array.from(videos).find(video => {
            const tracks = video.srcObject?.getVideoTracks() || [];
            const track = tracks[0];
            return track?.label?.startsWith('screen:') ||
                   track?.label?.startsWith('window:') ||
                   track?.label?.startsWith('web-contents-media-stream://');
          });
  
          // Calculate content area (excluding header)
          const contentHeight = source.height - pipDesign.header.height;
          const contentY = pipDesign.header.height;
  
          if (screenShareVideo && screenShareVideo.readyState === 4) {
            // If screen share exists, show only that in full screen but with design elements
            const videoAspect = screenShareVideo.videoWidth / screenShareVideo.videoHeight;
            const canvasAspect = source.width / contentHeight;
  
            let drawWidth, drawHeight, offsetX = 0, offsetY = contentY;
  
            if (videoAspect > canvasAspect) {
              drawWidth = source.width - (pipDesign.border.width * 2);
              drawHeight = drawWidth / videoAspect;
              offsetY = contentY + (contentHeight - drawHeight) / 2;
            } else {
              drawHeight = contentHeight - (pipDesign.border.width * 2);
              drawWidth = drawHeight * videoAspect;
              offsetX = (source.width - drawWidth) / 2;
            }
  
            try {
              // Draw rounded rectangle for screen share
              drawRoundedRect(
                ctx,
                offsetX,
                offsetY,
                drawWidth,
                drawHeight,
                pipDesign.border.radius
              );
  
              // Draw the screen share video
              ctx.save();
              ctx.clip();
              ctx.drawImage(
                screenShareVideo,
                offsetX,
                offsetY,
                drawWidth,
                drawHeight
              );
              ctx.restore();
  
              // Draw border
              ctx.strokeStyle = pipDesign.border.color;
              ctx.lineWidth = pipDesign.border.width;
              ctx.stroke();
  
              // Check if camera is enabled
              const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
              const isCameraEnabled = cameraTrack ? !cameraTrack.isMuted : false;
  
              // Check if microphone is enabled
              const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
              const isMicEnabled = micTrack ? !micTrack.isMuted : false;
  
              // Get speaking state from participant
              const isSpeaking = room.localParticipant.isSpeaking;
  

  
              // Get local participant's video track
              const localVideo = Array.from(videos).find(video => {
                const tracks = video.srcObject?.getVideoTracks() || [];
                const track = tracks[0];
                return track?.label?.includes('camera') || track?.label?.includes('Camera');
              });
  
       
  
              // Get all participants (local and remote)
              const allParticipants = [
                room.localParticipant,
                ...Array.from(room.remoteParticipants.values())
              ];
  
              // Track speaking participants with timestamps
              const speakingParticipants = allParticipants.filter(participant => {
                const isSpeaking = participant.isSpeaking;
                const micTrack = participant.getTrackPublication(Track.Source.Microphone);
                const isMicEnabled = micTrack ? !micTrack.isMuted : false;
                return isSpeaking && isMicEnabled;
              });
  
              // Sort by most recent speaking (if we have timestamps)
              speakingParticipants.sort((a, b) => {
                const aTime = a.lastSpeakingTime || 0;
                const bTime = b.lastSpeakingTime || 0;
                return bTime - aTime;
              });
  
              // Take only the most recent speakers up to maxSmallWindows
              const recentSpeakers = speakingParticipants.slice(0, pipDesign.maxSmallWindows);
  
              // Calculate small window dimensions
              const smallWindowWidth = source.width * 0.25;
              const smallWindowHeight = source.height * 0.25;
              const smallWindowSpacing = 10;
  
              // Show small windows for speaking participants
              recentSpeakers.forEach((participant, index) => {
                const smallWindowX = source.width - smallWindowWidth - 20;
                const smallWindowY = source.height - (smallWindowHeight + smallWindowSpacing) * (index + 1) - 20;
  
                // Draw rounded rectangle for small window
                drawRoundedRect(
                  ctx,
                  smallWindowX,
                  smallWindowY,
                  smallWindowWidth,
                  smallWindowHeight,
                  pipDesign.border.radius
                );
  
                // Draw animated border if speaking
                const time = Date.now() / 1000;
                const pulseIntensity = (Math.sin(time * 3) + 1) / 2;
                const borderWidth = 2 + pulseIntensity * 2;
                const borderColor = `rgba(30, 140, 250, ${0.5 + pulseIntensity * 0.5})`;
  
                ctx.strokeStyle = borderColor;
                ctx.lineWidth = borderWidth;
                ctx.stroke();
  
                // Draw avatar with first letter
                ctx.save();
                ctx.clip();
  
                // Draw background circle
                const centerX = smallWindowX + smallWindowWidth / 2;
                const centerY = smallWindowY + smallWindowHeight / 2;
                const radius = Math.min(smallWindowWidth, smallWindowHeight) / 2 - 10;
  
                // Animate circle size based on speaking state
                const pulseScale = 1 + (Math.sin(Date.now() / 200) * 0.1);
                const scaledRadius = radius * pulseScale;
  
                ctx.beginPath();
                ctx.arc(centerX, centerY, scaledRadius, 0, Math.PI * 2);
                ctx.fillStyle = '#1a1a1a';
                ctx.fill();
  
                // Draw first letter of name
                const name = participant.name || 'U';
                const avatarText = generateAvatar(name);
                // Adjust font size based on number of characters
                const fontSize = avatarText.length > 1 ? radius * 0.8 : radius;
                ctx.font = `bold ${fontSize}px Arial`;
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(avatarText, centerX, centerY);
  
                // Add small indicator for local participant
                if (participant.identity === room.localParticipant.identity) {
                  ctx.font = '12px Arial';
                  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                  ctx.fillText('You', centerX, centerY + radius + 15);
                }
  
                ctx.restore();
              });
  
            } catch (error) {
              console.error('Error in drawCanvas:', error);
              console.warn('Error drawing screen share:', error);
            }
          } else {
            // Show single window that switches between local and remote
            const cellWidth = source.width;
            const cellHeight = contentHeight;
  
            // Get all remote participants with their states
            const remoteParticipants = Array.from(room.remoteParticipants.values());
  
            // Find participants who are speaking
            const speakingParticipants = remoteParticipants.filter(p => p.isSpeaking);
  
            // Priority order for showing participants:
            // 1. Speaking remote participant
            // 2. Local participant
            let participantToShow = room.localParticipant;
  
            if (speakingParticipants.length > 0) {
              // If multiple people are speaking, show the most recent speaker
              participantToShow = speakingParticipants.sort((a, b) =>
                (b.lastSpeakingTime || 0) - (a.lastSpeakingTime || 0)
              )[0];
            }
  
            const isLocalParticipant = participantToShow.identity === room.localParticipant.identity;
  
            // Find the video for the participant to show (only for local participant)
            const videoToShow = isLocalParticipant ? Array.from(videos).find(video => {
              const tracks = video.srcObject?.getVideoTracks() || [];
              return tracks.some(track =>
                track.label?.includes('camera') || track.label?.includes('Camera')
              );
            }) : null;
  
            // Draw window
            drawRoundedRect(ctx, 0, contentY, cellWidth, cellHeight, pipDesign.border.radius);
            ctx.save();
            ctx.clip();
  
            // Only show video for local participant, always show avatar for remote
            const videoTrack = isLocalParticipant ? participantToShow.getTrackPublication(Track.Source.Camera) : null;
            const isVideoEnabled = isLocalParticipant && videoTrack && !videoTrack.isMuted && videoToShow && videoToShow.readyState === 4;
  
            if (isVideoEnabled) {
              // Draw video if enabled (only for local participant)
              ctx.drawImage(videoToShow, 0, contentY, cellWidth, cellHeight);
            } else {
              // Draw avatar
              const centerX = cellWidth / 2;
              const centerY = contentY + cellHeight / 2;
              const radius = Math.min(cellWidth, cellHeight) / 3;
  
              // Draw background circle with glow if speaking
              ctx.beginPath();
              ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
  
              // Add glow effect if speaking
              if (participantToShow.isSpeaking) {
                const time = Date.now() / 1000;
                const pulseIntensity = (Math.sin(time * 3) + 1) / 2;
                const glowRadius = radius + (pulseIntensity * 10); // Glow extends 10px max
  
                // Draw glow
                ctx.beginPath();
                ctx.arc(centerX, centerY, glowRadius, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(30, 140, 250, ${0.2 + pulseIntensity * 0.3})`;
                ctx.fill();
  
                // Draw main circle
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
              }
  
              ctx.fillStyle = '#1a1a1a';
              ctx.fill();
  
              // Draw avatar text
              const name = participantToShow.name || 'U';
              const avatarText = generateAvatar(name);
              const fontSize = avatarText.length > 1 ? radius * 0.8 : radius;
              ctx.font = `bold ${fontSize}px Arial`;
              ctx.fillStyle = '#ffffff';
              ctx.textAlign = 'center';
              ctx.textBaseline = 'middle';
              ctx.fillText(avatarText, centerX, centerY);
            }
            ctx.restore();
  
            // Draw border with glow effect if speaking
            if (participantToShow.isSpeaking) {
              // Animate border glow
              const time = Date.now() / 1000;
              const pulseIntensity = (Math.sin(time * 3) + 1) / 2;
              const borderWidth = 2 + pulseIntensity * 2;
              const borderColor = `rgba(30, 140, 250, ${0.5 + pulseIntensity * 0.5})`;
  
              ctx.strokeStyle = borderColor;
              ctx.lineWidth = borderWidth;
            } else {
              ctx.strokeStyle = pipDesign.border.color;
              ctx.lineWidth = pipDesign.border.width;
            }
            ctx.stroke();
          }
        } catch (error) {
          setToastNotification("Error toggling PIP mode.");
          setToastStatus("error");
          setShowToast(true);
          // console.log("Error toggling PIP mode.", error);
        }
  
        if (document.pictureInPictureElement === pipVideo) {
          requestAnimationFrame(drawCanvas);
        }
      }
  
      // Helper function to draw rounded rectangles
      function drawRoundedRect(ctx, x, y, width, height, radius) {
        if (width < 2 * radius) radius = width / 2;
        if (height < 2 * radius) radius = height / 2;
  
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
      }
  
      // Helper function to draw custom background
      function drawCustomBackground(ctx, width, height) {
        // Draw main background
        ctx.fillStyle = pipDesign.background.color;
        ctx.fillRect(0, 0, width, height);
  
        // Draw header
        ctx.fillStyle = pipDesign.header.backgroundColor;
        ctx.fillRect(0, 0, width, pipDesign.header.height);
  
        // Draw header text
        ctx.font = `${pipDesign.header.fontSize}px Arial, sans-serif`;
        ctx.fillStyle = pipDesign.header.textColor;
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
        ctx.fillText('Daakia', 12, pipDesign.header.height / 2);
      }
  
      // Helper function to draw logo or text
      function drawLogoOrText(ctx, width, height) {
        ctx.save();
  
        // Set font properties
        ctx.font = `${pipDesign.logo.fontSize}px Arial, sans-serif`;
        ctx.fillStyle = pipDesign.logo.fontColor;
        ctx.textBaseline = 'middle';
  
        const text = pipDesign.logo.text;
        const textWidth = ctx.measureText(text).width;
        const textHeight = pipDesign.logo.fontSize;
        const padding = 8;
  
        // Calculate position based on setting
        let x, y;
        switch (pipDesign.logo.position) {
          case 'topLeft':
            x = padding;
            y = pipDesign.header.height + padding + textHeight/2;
            ctx.textAlign = 'left';
            break;
          case 'topRight':
            x = width - padding;
            y = pipDesign.header.height + padding + textHeight/2;
            ctx.textAlign = 'right';
            break;
          case 'bottomLeft':
            x = padding;
            y = height - padding - textHeight/2;
            ctx.textAlign = 'left';
            break;
          case 'bottomRight':
          default:
            x = width - padding;
            y = height - padding - textHeight/2;
            ctx.textAlign = 'right';
            break;
        }
  
        // Draw text with subtle shadow
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
  
        ctx.fillText(text, x, y);
  
        ctx.restore();
      }
    } else {
      setToastNotification("Picture-in-Picture is not supported by your browser");
      setToastStatus("error");
      setShowToast(true);
    }
  };


useEffect(() => {
  if (isPIPEnabled == null) return;
  togglePipMode(isPIPEnabled);
}, [isPIPEnabled]);

  // Handle Event for participant  connected
  useEffect(() => {
    if (!room) return;

    const onParticipantConnected = (participant) => {
      // Find the participant in the list
      const participantIndex = privatechatparticipants.findIndex((part) => {
        return (
          part.participant.identity.toString() ===
          participant.identity.toString()
        );
      });

      if (participantIndex >= 0) {
        // Create a new array with the updated participant's isConnected status
        const updatedParticipants = [...privatechatparticipants];
        updatedParticipants[participantIndex] = {
          ...updatedParticipants[participantIndex],
          isConnected: true,
        };

        // Update the state with the new array
        setPrivateChatParticipants(updatedParticipants);

        // Update selectedPrivateChatParticipant if it matches the disconnected participant
        if (
          selectedPrivateChatParticipant &&
          selectedPrivateChatParticipant.participant.identity.toString() ===
            participant.identity.toString()
        ) {
          // Create a new object for the selected participant
          const updatedSelectedParticipant = {
            ...selectedPrivateChatParticipant,
            isConnected: true,
          };

          setSelectedPrivateChatParticipant(updatedSelectedParticipant);
        }
      }

      // if showRecordingConsentIcon is true, then add the participant to the participantConsent
      const participantMetadata = JSON.parse(participant?.metadata);
      if(showRecordingConsentIcon && participantMetadata?.role_name !== "moderator" && !showRecording){
        participantConsent.push(
          {
            participantId: participant?.identity,
            participantName: participant?.name,
            participantAvatar: generateAvatar(participant?.name),
            consent: "pending",
          }
        );
        setParticipantConsent(participantConsent);
        const encoder = new TextEncoder();
        const data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.RECORDING_CONSENT_MODAL,
            value: true,
          })
        );
        room.localParticipant.publishData(data, {
          reliable: true,
          destinationIdentities: [participant.identity],
        });
      }
    };

    room.on(RoomEvent.ParticipantConnected, onParticipantConnected);

    return () => {
      room.off(RoomEvent.ParticipantConnected, onParticipantConnected);
    };
  }, [room, privatechatparticipants, selectedPrivateChatParticipant,showRecordingConsentIcon,participantConsent, showRecording]);

  const extendMeeting = async () => {
    try {
      const response = await VideoConferenceService.extendMeeting(
        true,
        props.meetingDetails?.room_uid,
        props.isHost ? apiToken : coHostToken,
        devMode,
      );
      if (response?.success === 1) {
        setEndMeetingNotification(false);
        // add 10 min to meetingEndTime
        let meetingEndTime = autoMeetingEndTime;
        meetingEndTime = meetingEndTime.add(10, "minutes");
        props.meetingDetails.meeting_config.auto_meeting_end_schedule = meetingEndTime;
        setAutoMeetingEndTime(meetingEndTime);
        dismissed.current = false;
        // broadcast the event in data channel
        const encoder = new TextEncoder();
        const data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.EXTEND_MEETING_END_TIME,
          })
        );
        room.localParticipant.publishData(data, { reliable: true });
        setToastNotification("Meeting extended successfully!");
        setToastStatus("success");
        setShowToast(true);
      } else {
        setToastNotification("Error extending meeting!");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  // Check if meeting is finished
  useEffect(() => {
    if (!room || meetingFeatures?.is_basic === 0) return;
    const endDateTime = moment(props.meetingDetails?.end_date);
    const intervalId = setInterval(() => {
      const currentDateTime = moment();
      const timeDifference = endDateTime.diff(currentDateTime, "seconds");
      if (timeDifference <= 0) {
        props.setIsMeetingFinished(() => true);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
        room.disconnect();
        if (isElectronApp) {
          window?.electronAPI?.ipcRenderer?.send("stop-annotation");
        }
      } else if (timeDifference <= 300 && timeDifference >= 280) {
        // 5 minutes in seconds
        setToastNotification(`Meeting will end in 5 minutes`);
        setToastStatus("warning");
        setShowToast(true);
      }
    }, 30000); // Run every 30sec

    return () => clearInterval(intervalId); // Cleanup interval on unmount
  }, [room]);

  useEffect(() => {
    if (!room || props.meetingDetails?.meeting_config?.auto_meeting_end === 0) return;
    const intervalId = setInterval(() => {
      const currentDateTime = moment();
      const timeDifference = autoMeetingEndTime.diff(currentDateTime, "seconds");
      // If meeting has officially ended
      if (timeDifference <= 0) {
        props.setIsMeetingFinished(() => true);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
        room.disconnect();
        if (isElectronApp) {
          window?.electronAPI?.ipcRenderer?.send("stop-annotation");
        }
      } else if (timeDifference <= 600) {
        if(props.isHost || isCoHost){
          if(!dismissed.current){
            setEndMeetingNotification(true);
          }
        } else {
          if(!hasShownToast.current){
            setToastNotification(`Meeting will end in 10 minutes`);
            setToastStatus("warning");  
            setShowToast(true);
            hasShownToast.current = true;
          }
        }
      }
    }, 10000); // Run every 10 sec
  
    return () => clearInterval(intervalId); // Cleanup interval on unmount
  }, [room, autoMeetingEndTime]);

  useEffect(() => {
    // If screen share tracks are published, and no pin is set explicitly, auto set the screen share.
    if (
      screenShareTracks.some((track) => track.publication.isSubscribed) &&
      lastAutoFocusedScreenShareTrack.current === null
    ) {
      log.debug("Auto set screen share focus:", {
        newScreenShareTrack: screenShareTracks[0],
      });
      layoutContext.pin.dispatch?.({
        msg: "set_pin",
        trackReference: screenShareTracks[0],
      });
      lastAutoFocusedScreenShareTrack.current = screenShareTracks[0]; // eslint-disable-line
      if (isElectronApp && isScreenShareEnabled) {
        let userChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
        window?.electronAPI?.ipcRenderer?.send("minimize-main-window", {
          video: userChoice.video,
          audio: userChoice.audio,
          screenShareDisplayId,
        });
      }
    } else if (
      lastAutoFocusedScreenShareTrack.current &&
      !screenShareTracks.some(
        (track) =>
          track.publication.trackSid ===
          lastAutoFocusedScreenShareTrack.current?.publication?.trackSid
      )
    ) {
      log.debug("Auto clearing screen share focus.");
      layoutContext.pin.dispatch?.({ msg: "clear_pin" });
      lastAutoFocusedScreenShareTrack.current = null;
      if (isElectronApp) {
        window?.electronAPI?.ipcRenderer?.send("stop-annotation");
      }
    }
    if (focusTrack && !isTrackReference(focusTrack)) {
      const updatedFocusTrack = tracks.find(
        (tr) =>
          tr.participant.identity === focusTrack.participant.identity &&
          tr.source === focusTrack.source
      );
      if (
        updatedFocusTrack !== focusTrack &&
        isTrackReference(updatedFocusTrack)
      ) {
        layoutContext.pin.dispatch?.({
          msg: "set_pin",
          trackReference: updatedFocusTrack,
        });
      }
    }
  }, [
    screenShareTracks
      .map(
        (ref) => `${ref.publication.trackSid}_${ref.publication.isSubscribed}`
      )
      .join(),
    focusTrack?.publication?.trackSid,
    tracks,
  ]);

  useEffect(() => {
    if (!room) return;
    if (!connected) return;
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: showRaiseHand
          ? DataReceivedEvent.RAISE_HAND
          : DataReceivedEvent.STOP_RAISE_HAND,
      })
    );
    room.localParticipant.publishData(data, { reliable: true });
  }, [showRaiseHand, room, connected]);

  useEffect(() => {
    if (!room || !connected || !showEmojiReaction) return;
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: showEmojiReaction,
      })
    );
    room.localParticipant.publishData(data, { reliable: true });
  }, [showEmojiReaction, room, connected]);

  // Data Received
  useEffect(() => {
    if (!room) return;
    const decoder = new TextDecoder();
    const onDataReceived = async (payload, participant, kind) => {
      const strData = decoder.decode(payload);
      const data = JSON.parse(strData);
      if (data.action && data.action === DataReceivedEvent.RAISE_HAND) {
        setRemoteRaisedHands((prev) =>
          new Map(prev).set(participant.identity, true)
        );
      } else if (
        data.action &&
        data.action === DataReceivedEvent.STOP_RAISE_HAND
      ) {
        setRemoteRaisedHands((prev) => {
          const newMap = new Map(prev);
          newMap.delete(participant?.identity);
          return newMap;
        });
      } else if (
        data.action &&
        data.action === DataReceivedEvent.STOP_RAISE_HAND_ALL
      ) {
        setRemoteRaisedHands(new Map());
        setShowRaiseHand(false);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.ASK_TO_UNMUTE_MIC
      ) {
        // Ask to unmute mic
        setNotificationContent({
          title: `${participant.name} is asking you to turn on your mic`,
        });
        setNotificationAction("unmute");
        setNotificationVisible(true);
        if (isForceMuteAll) {
          setIsForceMuteAll(false);
        }
      } else if (data.action && data.action === DataReceivedEvent.MUTE_MIC) {
        room.localParticipant.setMicrophoneEnabled(false);
        setToastNotification("Microphone muted!");
        setToastStatus("info");
        setShowToast(true);
        // Mute mic
      } else if (
        data.action &&
        data.action === DataReceivedEvent.ASK_TO_UNMUTE_CAMERA
      ) {
        // Ask to unmute camera
        setNotificationContent({
          title: `${participant.name} is asking you to turn on your camera`,
        });
        setNotificationAction("videoOn");
        setNotificationVisible(true);
        if (isForceVideoOffAll) {
          setIsForceVideoOffAll(false);
        }
      } else if (data.action && data.action === DataReceivedEvent.MUTE_CAMERA) {
        room.localParticipant.setCameraEnabled(false);
        setToastNotification("Camera off!");
        setToastStatus("info");
        setShowToast(true);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.SEND_PRIVATE_MESSAGE
      ) {
        const { message, id, timestamp, isReplied, replyMessage } = data;
        // Find the participant in the private chat message map using identity
        const from = privatechatmessages.get(participant.identity);
        if (!from) {
          privatechatmessages.set(participant.identity, [
            {
              from: participant,
              message,
              id,
              timestamp,
              isReplied,
              replyMessage,
            },
          ]);
        } else {
          from.push({
            from: participant,
            message,
            id,
            timestamp,
            isReplied,
            replyMessage,
          });
        }
        setPrivateChatMessages(privatechatmessages);

        if (privatechatparticipants.length === 0) {
          setSelectedPrivateChatParticipant({
            key: 1,
            participant,
            isConnected: true,
            receivedUnreadMessagesCount: 1,
          });
          setPrivateChatParticipants([
            {
              key: 1,
              participant,
              isConnected: true,
              receivedUnreadMessagesCount: 1,
            },
          ]);
          setPrivateChatUnreadMessagesCount((prev) => prev + 1);
        } else {
          const participantIndex = privatechatparticipants.findIndex((part) => {
            return part.participant.identity === participant.identity;
          });
          if (participantIndex < 0) {
            const maxKey = privatechatparticipants.reduce(
              (max, p) => Math.max(max, p.key),
              0
            );
            const newParticipant = {
              key: maxKey + 1,
              participant,
              isConnected: true,
              receivedUnreadMessagesCount: 1,
            };
            setPrivateChatParticipants([
              ...privatechatparticipants,
              newParticipant,
            ]);
            setPrivateChatUnreadMessagesCount((prev) => prev + 1);
          } else {
            const updatedParticipants = [...privatechatparticipants];

            if (
              selectedPrivateChatParticipant?.participant.identity !==
              participant.identity
            ) {
              updatedParticipants[participantIndex] = {
                ...updatedParticipants[participantIndex],
                receivedUnreadMessagesCount:
                  updatedParticipants[participantIndex]
                    .receivedUnreadMessagesCount + 1,
              };
              setPrivateChatUnreadMessagesCount((prev) => prev + 1);
            } else if (!showPrivateChat) {
              const updatedSelectedParticipant = {
                ...selectedPrivateChatParticipant,
                receivedUnreadMessagesCount:
                  selectedPrivateChatParticipant.receivedUnreadMessagesCount +
                  1,
              };
              setSelectedPrivateChatParticipant(updatedSelectedParticipant);

              updatedParticipants[participantIndex] = {
                ...updatedParticipants[participantIndex],
                receivedUnreadMessagesCount:
                  updatedParticipants[participantIndex]
                    .receivedUnreadMessagesCount + 1,
              };
              setPrivateChatUnreadMessagesCount((prev) => prev + 1);
            }

            setPrivateChatParticipants(updatedParticipants);
          }
        }

        setNewMessageRender((prev) => prev + 1);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.SEND_PUBLIC_MESSAGE
      ) {
        const { message, id, timestamp, isReplied, replyMessage } = data;
        const mesg = {
          id: id,
          message: message,
          timestamp: timestamp,
          from: participant,
          isReplied,
          replyMessage,
        };
        setPublicChatMessages((prev) => [...prev, mesg]);
        setPublicChatUnreadMessagesCount((prev) => prev + 1);
      } else if (data.action && data.action === DataReceivedEvent.HEART) {
        setRemoteEmojiReactions((prev) =>
          new Map(prev).set(participant.identity, DataReceivedEvent.HEART)
        );
      } else if (data.action && data.action === DataReceivedEvent.BLUSH) {
        setRemoteEmojiReactions((prev) =>
          new Map(prev).set(participant.identity, DataReceivedEvent.BLUSH)
        );
      } else if (data.action && data.action === DataReceivedEvent.CLAP) {
        setRemoteEmojiReactions((prev) =>
          new Map(prev).set(participant.identity, DataReceivedEvent.CLAP)
        );
      } else if (data.action && data.action === DataReceivedEvent.SMILE) {
        setRemoteEmojiReactions((prev) =>
          new Map(prev).set(participant.identity, DataReceivedEvent.SMILE)
        );
      } else if (data.action && data.action === DataReceivedEvent.THUMBS_UP) {
        setRemoteEmojiReactions((prev) =>
          new Map(prev).set(participant.identity, DataReceivedEvent.THUMBS_UP)
        );
      } else if (
        data.action &&
        data.action === DataReceivedEvent.GRINNING_FACE
      ) {
        setRemoteEmojiReactions((prev) =>
          new Map(prev).set(
            participant.identity,
            DataReceivedEvent.GRINNING_FACE
          )
        );
      } else if (
        data.action &&
        data.action === DataReceivedEvent.MAKE_CO_HOST
      ) {
        setForceMute(data?.forceMute ?? forceMute);
        setForceVideoOff(data?.forceVideoOff ?? forceVideoOff);
        setCoHostToken(data.token);
        const tokenDetails = {
          token: data.token,
          meetingId: props.id,
          meeting_attendance_id: JSON.parse(room.localParticipant.metadata)
            ?.meeting_attendance_id,
          current_session_uid: parseInt(
            JSON.parse(room.localParticipant.metadata)?.current_session_uid,
            10
          ),
        };
        setLocalStorage(constants.CO_HOST_TOKEN, tokenDetails);
        setBreakoutRooms(data?.breakoutRooms ?? breakoutRooms);
        setAllowLiveCollabWhiteBoard(
          data?.allowLiveCollabWhiteBoard ?? allowLiveCollabWhiteBoard
        );
      } else if (
        data.action &&
        data.action === DataReceivedEvent.REMOVE_CO_HOST
      ) {
        setCoHostToken(null);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
        // removeLocalStorageItem(constants.CO_HOST_TOKEN);
      } else if (data.action && data.action === DataReceivedEvent.LOBBY) {
        if (
          props.isHost ||
          JSON.parse(room?.localParticipant.metadata)?.role_name === "cohost"
        ) {
          setLobbyParticipants((prev) => {
            const updatedMap = new Map(prev);
            const currentTime = Date.now();

            if (updatedMap.has(data.request_id)) {
              const existingData = updatedMap.get(data.request_id);
              updatedMap.set(data.request_id, {
                ...existingData,
                time: currentTime,
              });
            } else {
              updatedMap.set(data.request_id, { ...data, time: currentTime });
              // setToastNotification(`${data.display_name} wants to join`);
              setToastStatus("content");
              setToastNotification(
                <AllowParticipant
                  participantName={data.display_name}
                  setLobbyParticipants={setLobbyParticipants}
                  lobbyParticipants={lobbyParticipants}
                  requestId={data.request_id}
                  id={props.id}
                  setShowToast={setShowToast}
                  coHostToken={coHostToken}
                  apiToken={apiToken}
                  isCoHost={isCoHost}
                  devMode={devMode}
                />
              );
              setShowToast(true);
              new Audio(sounds.LOBBY_ENTER).play();
            }

            return updatedMap;
          });
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.FORCE_MUTE_ALL
      ) {
        if (
          JSON.parse(room?.localParticipant.metadata)?.role_name !== "cohost" &&
          JSON.parse(room?.localParticipant.metadata)?.role_name !== "moderator"
        ) {
          room.localParticipant.setMicrophoneEnabled(false);
          setIsForceMuteAll(data.value);
        }
        setForceMute(data.value);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.FORCE_VIDEO_OFF_ALL
      ) {
        if (
          JSON.parse(room?.localParticipant.metadata)?.role_name !== "cohost" &&
          JSON.parse(room?.localParticipant.metadata)?.role_name !== "moderator"
        ) {
          room.localParticipant.setCameraEnabled(false);
          setIsForceVideoOffAll(data.value);
        }
        setForceVideoOff(data.value);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.BR_MOVE_PARTICIPANT
      ) {
        try {
          if (data.participant_id.includes(room.localParticipant.identity)) {
            const response = await BreakoutRoomService.getBreakoutRoomToken(
              room.localParticipant,
              `${data?.to_meeting_uid}`,
              devMode
            );
            if (response?.success === 0) {
              console.log("Error getting breakout room token", response);
              return;
            }
            if (response?.data?.access_token) {
              setIsUsingBreakoutRoom(true);
              setIsMovingToRoom(true);
              setMovingRoomToken(response?.data?.access_token?.token);
              if (
                JSON.parse(room?.localParticipant?.metadata)?.role_name ===
                  "moderator" ||
                JSON.parse(room?.localParticipant?.metadata)?.role_name ===
                  "cohost"
              ) {
                await fetchBreakoutRoomDetails();
              }
            }
          }
        } catch (error) {
          // console.log("Error getting breakout room token", error);
          setToastNotification("Error moving to breakout room");
          setToastStatus("error");
          setShowToast(true);
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.BREAKOUT_ROOM_UPDATE
      ) {
        if (
          JSON.parse(room?.localParticipant?.metadata)?.role_name ===
            "moderator" ||
          JSON.parse(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          let activeRoom = 0;
          setBreakoutRooms(data.breakoutRooms);
          if (Object.keys(data.breakoutRooms).length <= 2) {
            // Use Object.keys to iterate only over own properties
            Object.keys(data.breakoutRooms).forEach((key) => {
              if (!data.breakoutRooms[key].isDeleted) {
                activeRoom += 1;
              }
            });
            if (activeRoom <= 1) {
              setDrawerState(DrawerState.PARTICIPANTS);
              setIsBreakoutRoomCnfigSet(false);
            }
          }
        }
      } else if (data.action && data.action === DataReceivedEvent.LIVECAPTION) {
        setLiveCaptionData(data);
        setLiveCaptionsObject((prev) => ({
          ...prev,
          showIcon: true,
        }));
      } else if (
        data.action &&
        data.action === DataReceivedEvent.SHOW_LIVECAPTION
      ) {
        setLiveCaptionsObject({
          showIcon: data.liveCaptionsData.showIcon,
          isLanguageSelected: data.liveCaptionsData.isLanguageSelected,
          langCode: data.liveCaptionsData.langCode,
        });
        setDrawerState(DrawerState.LIVECAPTION);
        setToastNotification("Live caption is enabled");
        setToastStatus("info");
        setShowToast(true);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.REQUEST_LIVECAPTION_DRAWER_STATE
      ) {
        if (liveCaptionsObject.showIcon) {
          const encoder = new TextEncoder();
          const data = encoder.encode(
            JSON.stringify({
              action: DataReceivedEvent.SHOW_LIVECAPTION,
              liveCaptionsData: { ...liveCaptionsObject },
            })
          );
          room?.localParticipant.publishData(data, {
            reliable: true,
            destinationIdentities: [participant?.identity],
          });
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.CAN_DOWNLOAD_CHAT_ATTACHEMENT
      ) {
        setCanDownloadChatAttachment(data.value);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.WHITEBOARD_STATE
      ) {
        if(data?.value){
          setToastNotification("Whiteboard Opened");
          setToastStatus("info");
          setShowToast(true);
        }else{
          setToastNotification("Whiteboard Closed");
          setToastStatus("info");
          setShowToast(true);
        }
        setIsWhiteboardOpen(data?.value);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.ALLOW_LIVE_COLLAB_WHITEBOARD
      ) {
        setAllowLiveCollabWhiteBoard(data.value);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.WHITEBOARD_DATA_REQUEST
      ) {
        if (isSocketConnected) {
          setTimeout(() => {
            socket.emit(SocketChannel.WHITEBOARD_UPDATE, {
              elements: whiteboardData?.elements,
            });
          }, 500); // 500 milliseconds delay
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.EXTEND_MEETING_END_TIME
      ) {
        setAutoMeetingEndTime((prev) => prev.add(10, "minutes"));
        setToastNotification("Meeting extended by 10 minutes");
        setToastStatus("info");
        setShowToast(true);
      }else if(data.action && data.action === DataReceivedEvent.RECORDING_CONSENT_MODAL){
        setIsRecordingConsentModalOpen(data.value);
        setShowDeniedModal(false);
      }else if (data.action && data.action === DataReceivedEvent.RECORDING_CONSENT_STATUS){
        // Find the participant in the participantConsent array
        const participantIndex = participantConsent.findIndex(p => p.participantId === participant?.identity);

        if (participantIndex !== -1) {
          // Update existing participant's consent status
          const updatedConsent = [...participantConsent];
          updatedConsent[participantIndex] = {
            ...updatedConsent[participantIndex],
            consent: data.consent
          };
          setParticipantConsent(updatedConsent);
        } else {
          // Add new participant with consent status
          setParticipantConsent(prev => [...prev, {
            participantName: participant?.name,
            participantId: participant?.identity,
            consent: data.consent
          }]);
        }
    if (data.consent === "reject") {
      setToastNotification("Some participants have rejected the recording consent");
      setToastStatus("error");
      setShowToast(true);
    }
    }else if(data.action && data.action === DataReceivedEvent.STARTED_RECORDING_CONSENT){
      setParticipantConsent(data.participants);
      setShowRecordingConsentIcon(true);
    }
    };
    room.on(RoomEvent.DataReceived, onDataReceived);
    return () => {
      room.off(RoomEvent.DataReceived, onDataReceived);
    };
  }, [
    room,
    isCoHost,
    coHostToken,
    lobbyParticipants,
    privatechatmessages,
    privateChatUnreadMessagesCount,
    selectedPrivateChatParticipant,
    whiteboardData,
    isSocketConnected,
    socket,
    liveCaptionsObject,
    participantConsent
  ]);

  // Co-host metadata?.role_name change
  useEffect(() => {
    if (!room) return;
    const onParticipantMetadataChanged = (_, participant) => {
      if (
        JSON.parse(participant.metadata)?.role_name === "cohost" &&
        room.localParticipant.identity === participant.identity
      ) {
        setIsCoHost(true);
        setLiveCaptionsObject((prev) => ({
          ...prev,
          showIcon: true,
        }));
      } else if (
        JSON.parse(participant.metadata)?.role_name === "participant" &&
        room.localParticipant.identity === participant.identity
      ) {
        setIsCoHost(false);
      }
    };
    room.on(RoomEvent.ParticipantMetadataChanged, onParticipantMetadataChanged);
    return () => {
      room.off(
        RoomEvent.ParticipantMetadataChanged,
        onParticipantMetadataChanged
      );
    };
  }, [room]);

  // Connection state change
  useEffect(() => {
    if (!room) return;
    const onConnectionStateChange = (state) => {
      setConnected(state === ConnectionState.Connected);
      setConnectionState(state);
    };
    room.on(RoomEvent.ConnectionStateChanged, onConnectionStateChange);
    return () => {
      room.off(RoomEvent.ConnectionStateChanged, onConnectionStateChange);
    };
  }, [room]);

  // Disconnect reason
  useEffect(() => {
    const onDisconnected = (disconnectReason) => {
      switch (disconnectReason) {
        case DisconnectReason.CLIENT_INITIATED:
          if (props.isMeetingFinished) {
            // window.location.href = meetingEndedPageUrl;
            onClose(
              DISCONNECT_REASON.CLIENT_INITIATED,
              room?.localParticipant?.participantInfo
            );
            // triggerEvent(CUSTOM_EVENTS.MEETING_ENDED, eventHandler);
          } else {
            // window.location.href = meetingLeftPageUrl;
            onClose(
              DISCONNECT_REASON.CLIENT_INITIATED,
              room?.localParticipant?.participantInfo
            );
            // triggerEvent(CUSTOM_EVENTS.LEFT_MEETING, eventHandler);
          }
          break;
        case DisconnectReason.ROOM_DELETED:
          // window.location.href = meetingEndedPageUrl;
          onClose(
            DISCONNECT_REASON.ROOM_ENDED,
            room?.localParticipant?.participantInfo
          );
          // triggerEvent(CUSTOM_EVENTS.MEETING_ENDED, eventHandler);
          break;
        case DisconnectReason.PARTICIPANT_REMOVED:
          // window.location.href = participantRemovedPageUrl;
          onClose(
            DISCONNECT_REASON.PARTICIPANT_REMOVED,
            room?.localParticipant?.participantInfo
          );
          // triggerEvent(CUSTOM_EVENTS.PARTICIPANT_REMOVED, eventHandler);
          break;
        case DisconnectReason.UNKNOWN_REASON:
          // window.location.href = meetingLeftPageUrl;
          onClose(
            DISCONNECT_REASON.UNKNOWN_REASON,
            room?.localParticipant?.participantInfo
          );
          // triggerEvent(CUSTOM_EVENTS.LEFT_MEETING, eventHandler);
          break;
        default:
          // window.location.href = meetingLeftPageUrl;
          onClose(
            DISCONNECT_REASON.UNKNOWN_REASON,
            room?.localParticipant?.participantInfo
          );
        // triggerEvent(CUSTOM_EVENTS.LEFT_MEETING, eventHandler);
      }
    };
    room.on(RoomEvent.Disconnected, onDisconnected);
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected);
    };
  }, [room]);

  // Track muted/unmuted
  useEffect(() => {
    if (!room) return;
    const onRefresh = () => {
      setRefresh((prev) => prev + 1);
    };
    room.on(RoomEvent.TrackMuted, onRefresh);
    room.on(RoomEvent.TrackUnmuted, onRefresh);
    return () => {
      room.off(RoomEvent.TrackMuted, onRefresh);
      room.off(RoomEvent.TrackUnmuted, onRefresh);
    };
  }, [room]);

  const fetchBreakoutRoomConfig = async () => {
    try {
      const response = await BreakoutRoomService.getBreakoutRoomConfig(
        props.meetingDetails?.room_uid,
        props.isHost ? apiToken : coHostToken,
        room?.localParticipant?.participantInfo,
        devMode
      );
      if (response?.success === 0) {
        console.log("Error getting breakout room config", response);
        return;
      }
      return response;
    } catch (error) {
      console.log("Error getting breakout room config", error);
    }
  };

  // Initial Use Effect
  const breakoutRoomEndTime = useRef(null);

  useEffect(() => {
    const fetchSipConfig = async () => {
      if (room?.state === "connected") {
        const sipResponse = await VideoConferenceService.sipConnection(
          props.id,
          props.isHost ? apiToken : coHostToken,
          room?.localParticipant?.participantInfo,
          devMode
        );
        if (sipResponse?.success === 1) {
          setSipData(sipResponse?.data);
        }
      }
    };
    const fetchConfig = async () => {
      // set the room name
      if (room?.state === "connected") {
        if (
          JSON.parse(room?.localParticipant?.metadata)?.role_name ===
          "moderator"
        ) {
          await fetchBreakoutRoomDetails();
        }
        const roomNumber = room?.roomInfo?.name.split("__BR")[1];
        const roomkey = roomNumber || "0";
        const roomName = roomNumber ? `Breakout Room ${roomkey}` : "Main Room";
        setCurrentRoomName(roomName);
        setIsBreakoutRoom(!!roomNumber);
        if (
          JSON.parse(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          const tokenData = getLocalStorage(constants.CO_HOST_TOKEN);
          if (
            tokenData &&
            tokenData.token &&
            tokenData.meetingId === props.id
          ) {
            setCoHostToken(tokenData.token);
            setIsCoHost(true);
          }
        }
        if (roomkey !== "0") {
          const response = await fetchBreakoutRoomConfig();
          if (response?.success === 1) {
            response?.data?.forEach((config) => {
              if (config?.room_uid === room?.roomInfo?.name) {
                const startTime = moment(config?.start_datetime).local();

                const currentTime = moment();

                const differenceInSeconds = currentTime.diff(
                  startTime,
                  "seconds"
                );
                setBreakoutRoomDuration(config?.auto_timeout);
                const timeoutSeconds = config?.auto_timeout * 60;
                const leftTimeout = timeoutSeconds - differenceInSeconds;

                if (leftTimeout > 0) {
                  setEndBreakoutRoomTimer(leftTimeout);
                  breakoutRoomEndTime.current = moment().add(
                    leftTimeout,
                    "seconds"
                  );
                } else {
                  // setBreakoutRoomDuration(0);
                  breakoutRoomEndTime.current = 0;
                  setEndBreakoutRoomTimer(0);
                }
              }
            });
          }
        } else {
          const response = await fetchBreakoutRoomConfig();
          if (response?.success === 1) {
            setBreakoutRoomDuration(response?.data[0]?.auto_timeout || 5);
            if (
              response?.data[0]?.auto_timeout &&
              Object.keys(breakoutRooms).length > 1
            ) {
              setIsBreakoutRoomCnfigSet(true);
            }
          }
        }
      }
    };

    const requestWhiteboardData = async () => {
      if (room?.state === "connected") {
        const response = await WhiteboardService.getWhiteboardDetail(
          props.meetingDetails.id,
          props.isHost ? apiToken : coHostToken,
          room?.localParticipant?.participantInfo,
          devMode
        );
        if (response.success !== 0) {
          const remoteParticipants = Array.from(
            room?.remoteParticipants.values()
          );
          if (
            response.data[0]?.status === "open" &&
            remoteParticipants.length > 0
          ) {
            const encoder = new TextEncoder();
            const data = encoder.encode(
              JSON.stringify({
                action: DataReceivedEvent.WHITEBOARD_DATA_REQUEST,
              })
            );
            const participant = remoteParticipants[0]?.identity;
            room?.localParticipant.publishData(data, {
              reliable: true,
              destinationIdentities: [participant],
            });
          }
          setWhiteboardId(response.data[0]?.id);
          setIsWhiteboardOpen(response.data[0]?.status === "open");
        }
      }
    };
    if (room?.state === "connected" && !props.isHost && !isCoHost) {
      setIsLiveCaptionsDrawerOpen(
        props.meetingDetails?.transcription_detail?.transcription_enable
      );
    }

    const requestLiveCaptionsStateData = () => {
      if (room?.state === "connected") {
        const encoder = new TextEncoder();
        const remoteParticipants = Array.from(
          room?.remoteParticipants.values()
        );
        const participant = remoteParticipants.find((part) => {
          try {
            const metadata = part?.metadata ? JSON.parse(part.metadata) : null;
            return metadata?.role_name === "participant";
          } catch (error) {
            setToastNotification(error.message);
            setToastStatus("error");
            setShowToast(true);
            console.error("Invalid metadata JSON:", part?.metadata, error);
            return false;
          }
        });
        if (participant) {
          const data = encoder.encode(
            JSON.stringify({
              action: DataReceivedEvent.REQUEST_LIVECAPTION_DRAWER_STATE,
            })
          );
          room?.localParticipant.publishData(data, {
            reliable: true,
            destinationIdentities: [participant?.identity],
          });
        }
      }
    };

    const checkForAutoRecording = async () => {
      if (
        room?.state === "connected" &&
        props.meetingDetails?.meeting_config?.auto_start_recording === 1 &&
        props.meetingDetails?.meeting_config?.recording_force_stopped === 0 &&
        !props.meetingDetails?.is_recording_active &&
        (JSON.parse(room?.localParticipant?.metadata)?.role_name ===
          "moderator" ||
          JSON.parse(room?.localParticipant?.metadata)?.role_name === "cohost")
      ) {
        await new Promise((resolve) => setTimeout(resolve, 6000)); // added delay for room setup
        await SettingsMenuServices.startCloudRecording(
          props.id,
          props.isHost ? apiToken : coHostToken,
          room?.localParticipant?.participantInfo,
          devMode
        );
      }
    };

    const checkForCohost = async () => {
      try {
        if (room?.state !== "connected") return;
        const cohostDetail = getLocalStorage(constants.CO_HOST_TOKEN);
        if (
          cohostDetail &&
          cohostDetail.token &&
          cohostDetail.meetingId === props.id &&
          JSON.parse(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          setCoHostToken(coHostToken?.token);
          setIsCoHost(true);
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.log("Error: ", error);
      }
    };

    const getParticipantConsentList = async () => {
      try {
        if(room?.state === "connected") {
          const metadata = JSON.parse(room?.localParticipant?.metadata);
          if(showRecordingConsentIcon && metadata?.current_session_uid) {
            const response = await SettingsMenuServices.getParticipantConsentList(props.id, metadata?.current_session_uid,devMode);
            if (response.success === 1) {
              const consentData = response.data.map(item => ({
                participantId: item?.rtc_participant_uid,
                participantName: item?.screen_name,
                participantAvatar: generateAvatar(item?.screen_name),
                consent: item?.consent || "pending",
              }));
              setParticipantConsent(consentData);
            }
          }
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.log("Error: ",error)
      }
    };

    const checkRecordingIsActive = () => {
      if (room?.state === "connected" && props.meetingDetails?.is_recording_active) {
        new Audio(sounds.RECORDING_START).play();
        setToastNotification("This meeting is being recorded...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(true);
      }
    }

    checkRecordingIsActive();

    checkForCohost();
    checkForAutoRecording();
    requestLiveCaptionsStateData();
    requestWhiteboardData();
    fetchConfig();
    fetchSipConfig();
    getParticipantConsentList();
  }, [room?.state]);

  // Recording status change
  useEffect(() => {
    if (!room) return;
    const onRecordingStatusChange = (isRecording) => {
      // console.log("Recording status changed", isRecording);
      if (isRecording) {
        new Audio(sounds.RECORDING_START).play();
        setToastNotification("Recording started...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(true);
      } else {
        new Audio(sounds.RECORDING_STOP).play();
        setToastNotification("Recording stopped...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(false);
        setShowRecordingConsentIcon(false);
      }
    };
    room.on(RoomEvent.RecordingStatusChanged, onRecordingStatusChange);
    return () => {
      room.off(RoomEvent.RecordingStatusChanged, onRecordingStatusChange);
    };
  }, [room]);

  // Timer to hide toast notification after 10 seconds
  useEffect(() => {
    if (!room) return;

    const timer = setTimeout(() => {
      if (showToast) setShowToast(false);
    }, 10000);

    // Clear the timeout if the component unmounts or if showToast changes
    return () => clearTimeout(timer);
  }, [showToast]);

  // Timer to end breakout room
  useEffect(() => {
    if (!room) return;
    if (!isBreakoutRoom || breakoutRoomEndTime.current === null) return;
    const intervalId = setInterval(async () => {
      const currentTime = moment();
      setEndBreakoutRoomTimer(
        breakoutRoomEndTime.current.diff(currentTime, "seconds")
      );
      if (endBreakoutRoomTimer <= 0) {
        const response = await BreakoutRoomService.getBreakoutRoomToken(
          room.localParticipant,
          `${props.meetingDetails?.room_uid}`,
          devMode
        );
        if (response?.success === 0) {
          console.log("Error getting breakout room token", response);
          return;
        }
        if (response?.data?.access_token) {
          setIsMovingToRoom(true);
          setMovingRoomToken(response?.data?.access_token?.token);
          if (
            JSON.parse(room?.localParticipant?.metadata)?.role_name ===
            "moderator"
          ) {
            await fetchBreakoutRoomDetails();
          }
        }
        breakoutRoomEndTime.current = null;
      }
    }, 1000);
    return () => clearInterval(intervalId);
  }, [room, isBreakoutRoom, endBreakoutRoomTimer, isCoHost]);

  const formatEndBreakoutRoomTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  const [showChatDrawer, setShowChatDrawer] = useState(false);

  // Alert on back button press
  const [isAlertVisible, setIsAlertVisible] = useState(false);

  const onBackButtonEvent = (e) => {
    e.preventDefault(); // Prevent default back navigation
    if (!isAlertVisible) {
      e.preventDefault(); // Prevent default back navigation
      setIsAlertVisible(true); // Show alert confirmation
    } else {
      e.preventDefault(); // Prevent default back navigation
      setIsAlertVisible(false); // Hide alert confirmation
    }
  };
  const handleConfirm = () => {
    setIsAlertVisible(false);
    room.disconnect();
    if (isElectronApp) {
      window?.electronAPI?.ipcRenderer?.send("stop-annotation");
    }
  };
  const handleCancel = () => {
    setIsAlertVisible(false);
    window.history.pushState(null, null, window.location.pathname);
  };
  useEffect(() => {
    window.addEventListener("popstate", onBackButtonEvent);

    window.history.pushState(null, null, window.location.pathname);
    return () => {
      window.removeEventListener("popstate", onBackButtonEvent);
    };
  }, []);

  // Storing the final translated captions
  useEffect(() => {
    // Check if translationDetails?.target_lang is defined and changes
    if (translationDetails?.target_lang && targetLangChangedFor === 0) {
      setTargetLangChangedFor(1);
      setFinalTranslatedCaptions([...finalCaptions]);
    } else if (translationDetails?.target_lang && targetLangChangedFor !== 0) {
      setFinalTranslatedCaptions((prevCaptions) => [...prevCaptions]);
    }
  }, [translationDetails?.target_lang]);

  useEffect(() => {
    if (room) {
      if (focusTrack) {
        setIsFocusTrackEnabled(true);
      } else {
        setIsFocusTrackEnabled(false);
      }
    }
  }, [room, focusTrack]);

  return isPipWindow ? (
    <PipLayout
      layoutContext={layoutContext}
      focusTrack={focusTrack}
      carouselTracks={carouselTracks}
      // setIsPipWindow={setIsPipWindow}
      // isPipWindow={isPipWindow}
      isForceMuteAll={isForceMuteAll}
      isCoHost={isCoHost}
      isHost={props.isHost}
      isForceVideoOffAll={isForceVideoOffAll}
      isScreenShareEnabled={isScreenShareEnabled}
      // setIsScreenShareEnabled={setIsScreenShareEnabled}
      onScreenShareChange={onScreenShareChange}
      screenShareMode={screenShareMode}
      maxWidth={props.maxWidth}
      maxHeight={props.maxHeight}
      showEmojiReaction={showEmojiReaction}
      setShowEmojiReaction={setShowEmojiReaction}
      showRaiseHand={showRaiseHand}
      remoteRaisedHands={remoteRaisedHands}
      remoteEmojiReactions={remoteEmojiReactions}
      setRemoteEmojiReactions={setRemoteEmojiReactions}
    />
  ) : (
    <div
      className={`lk-video-conference ${
        isElectron() && "lk-video-conference-electron"
      }`}
      {...props}
      style={{ height: "100svh" }}
    >
      {isMobileBrowser()
        ? isAlertVisible && (
            <div className="back-alert-mobile">
              <p>Do you really want to go back?</p>
              <div className="back-alert-confirm">
                <Button type="primary" onClick={handleConfirm}>
                  Yes
                </Button>
                <Button onClick={handleCancel}>No</Button>
              </div>
            </div>
          )
        : isAlertVisible && (
            <div className="back-alert-web">
              <div className="back-alert-web-box">
                <p>Do you really want to go back?</p>
                <div className="back-alert-confirm">
                  <Button type="primary" onClick={handleConfirm}>
                    Yes
                  </Button>
                  <Button onClick={handleCancel}>No</Button>
                </div>
              </div>
            </div>
          )}
      {isBreakoutRoom && (
        <div className="br-timer">
          {formatEndBreakoutRoomTime(endBreakoutRoomTimer)}
        </div>
      )}
      {isWeb() && (
        <LayoutContextProvider
          value={layoutContext}
          onWidgetChange={widgetUpdate}
        >
          <div className="lk-video-conference-inner">
            {!isScreenShareEnabled &&
              !isFocusTrackEnabled &&
              !isWhiteboardOpen && (
                meetingFeatures?.branding_enabled === 1 
                 ? (
                  meetingFeatures?.branding_logo_url !== null && (
                    <img
                      src={meetingFeatures?.branding_logo_url}
                      alt="Logo"
                      className={`daakia-logo ${
                        isElectron() && "daakia-logo-electron"
                      }`}
                    />
                  )) : (
                  <DaakiaLogo
                    className={`daakia-logo ${
                      isElectron() && "daakia-logo-electron"
                    }`}
                  />
                )
              )}
            {!focusTrack ? (
              isWhiteboardOpen ? (
                <div className="lk-focus-layout-wrapper whiteboard-focus">
                  <CarouselLayout tracks={carouselTracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </CarouselLayout>
                  <Whiteboard
                    room={room}
                    isWhiteboardOpen={isWhiteboardOpen}
                    setIsWhiteboardOpen={setIsWhiteboardOpen}
                    // socket={socket}
                    token={props.token}
                    isHost={props.isHost}
                    allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                    isCoHost={isCoHost}
                    whiteboardData={whiteboardData}
                    setWhiteboardData={setWhiteboardData}
                    socket={socket}
                    isSocketConnected={isSocketConnected}
                    isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
                    setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
                    whiteboardSceneData={whiteboardSceneData}
                    setWhiteboardSceneData={setWhiteboardSceneData}
                    meetingId={props.id}
                    whiteBoardId={whiteboardId}
                    meetingDetails={props.meetingDetails}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                    coHostToken={coHostToken}
                    apiToken={apiToken}
                    meetingFeatures={meetingFeatures}
                    devMode={devMode}
                  />
                  {/* Chat Drawer */}
                  {meetingFeatures?.conference_chat === 1 &&
                    (isMobileBrowser() ? (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        meetingFeatures={meetingFeatures}
                      />
                    ) : (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        meetingFeatures={meetingFeatures}
                      />
                    ))}
                  {/* Live Captions */}
                  {meetingFeatures?.voice_transcription === 1 &&
                    isLiveCaptionsDrawerOpen && (
                      <LiveCaptionsDrawer
                        isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
                        setIsLiveCaptionsDrawerOpen={
                          setIsLiveCaptionsDrawerOpen
                        }
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        liveCaptionData={liveCaptionData}
                        meetingDetails={props.meetingDetails}
                        livecaptionsobject={liveCaptionsObject}
                        setlivecaptionsobject={setLiveCaptionsObject}
                        setfinalcaptions={setFinalCaptions}
                        finalcaptions={finalCaptions}
                        id={props.id}
                        isHost={props.isHost}
                        coHostToken={coHostToken}
                        isWhiteboardOpen={isWhiteboardOpen}
                        setDrawerState={setDrawerState}
                        translationDetails={translationDetails}
                        setTranslationDetails={setTranslationDetails}
                        finalTranslatedCaptions={finalTranslatedCaptions}
                        setFinalTranslatedCaptions={setFinalTranslatedCaptions}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        meetingFeatures={meetingFeatures}
                        apiToken={apiToken}
                        devMode={devMode}
                      />
                    )}
                  {/* Participant Drawer */}
                  {showParticipantsList &&
                    connected &&
                    (isMobileBrowser() ? (
                      <div className="side-drawer-mobile">
                        <ParticipantList
                          remoteParticipants={room.remoteParticipants}
                          localParticipant={room.localParticipant}
                          showParticipantsList={showParticipantsList}
                          setShowParticipantsList={setShowParticipantsList}
                          id={props.id}
                          isHost={props.isHost}
                          layoutContext={layoutContext}
                          isCoHost={isCoHost}
                          lobbyParticipants={lobbyParticipants}
                          setLobbyParticipants={setLobbyParticipants}
                          setRemoteRaisedHands={setRemoteRaisedHands}
                          coHostToken={coHostToken}
                          setDrawerState={setDrawerState}
                          breakoutRooms={breakoutRooms}
                          setShowRaiseHand={setShowRaiseHand}
                          currentRoomName={currentRoomName}
                          setBreakoutRoomDuration={setBreakoutRoomDuration}
                          meetingDetails={props.meetingDetails}
                          setBreakoutRooms={setBreakoutRooms}
                          setRoomKeyCounter={setRoomKeyCounter}
                          isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                          setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                          apiToken={apiToken}
                          meetingFeatures={meetingFeatures}
                          setselectedprivatechatparticipant={
                            setSelectedPrivateChatParticipant
                          }
                          setprivatechatparticipants={
                            setPrivateChatParticipants
                          }
                          privatechatparticipants={privatechatparticipants}
                          setshowprivatechat={setShowPrivateChat}
                          forcemute={forceMute}
                          forcevideooff={forceVideoOff}
                          room={room}
                          isBreakoutRoom={isBreakoutRoom}
                          allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                          isWhiteboardOpen={isWhiteboardOpen}
                          isPinned={isPinned}
                          setIsPinned={setIsPinned}
                          setToastNotification={setToastNotification}
                          setToastStatus={setToastStatus}
                          setShowToast={setShowToast}
                          devMode={devMode}
                        />
                      </div>
                    ) : (
                      <ParticipantList
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        showParticipantsList={showParticipantsList}
                        setShowParticipantsList={setShowParticipantsList}
                        id={props.id}
                        isHost={props.isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        lobbyParticipants={lobbyParticipants}
                        setLobbyParticipants={setLobbyParticipants}
                        setRemoteRaisedHands={setRemoteRaisedHands}
                        coHostToken={coHostToken}
                        setDrawerState={setDrawerState}
                        breakoutRooms={breakoutRooms}
                        setShowRaiseHand={setShowRaiseHand}
                        currentRoomName={currentRoomName}
                        setBreakoutRoomDuration={setBreakoutRoomDuration}
                        meetingDetails={props.meetingDetails}
                        setBreakoutRooms={setBreakoutRooms}
                        setRoomKeyCounter={setRoomKeyCounter}
                        isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                        apiToken={apiToken}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        setprivatechatparticipants={setPrivateChatParticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setShowPrivateChat}
                        forcemute={forceMute}
                        forcevideooff={forceVideoOff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        isWhiteboardOpen={isWhiteboardOpen}
                        isPinned={isPinned}
                        setIsPinned={setIsPinned}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        devMode={devMode}
                      />
                    ))}
                  {/* Host Controls */}
                  {showHostControl && (
                    <HostControlDrawer
                      showHostControl={showHostControl}
                      setShowHostControl={setShowHostControl}
                      room={room}
                      setForceVideoOff={setForceVideoOff}
                      forceVideoOff={forceVideoOff}
                      setForceMute={setForceMute}
                      forceMute={forceMute}
                      setCanDownloadChatAttachment={
                        setCanDownloadChatAttachment
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      setAllowLiveCollabWhiteBoard={
                        setAllowLiveCollabWhiteBoard
                      }
                      allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                    />
                  )}
                  {/* Breakout Rooms */}
                  {meetingFeatures?.breakout_room === 1 && showBreakoutRoom && (
                    <BreakoutRoomDrawer
                      showBreakoutRoom={showBreakoutRoom}
                      setShowBreakoutRoom={setShowBreakoutRoom}
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      breakoutRooms={breakoutRooms}
                      setBreakoutRooms={setBreakoutRooms}
                      id={props.id}
                      setRoomKeyCounter={setRoomKeyCounter}
                      roomKeyCounter={roomKeyCounter}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      breakoutRoomDuration={breakoutRoomDuration}
                      meetingDetails={props.meetingDetails}
                      apiToken={apiToken}
                      coHostToken={coHostToken}
                      isHost={props.isHost}
                      setDrawerState={setDrawerState}
                      setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                      isWhiteboardOpen={isWhiteboardOpen}
                      devMode={devMode}
                    />
                  )}
                  {/* Virtual Background Drawer */}
                  {isVBDrawerOpen && (
                    <VirtualBackgroundDrawer
                      isVBDrawerOpen={isVBDrawerOpen}
                      setIsVBDrawerOpen={setIsVBDrawerOpen}
                      room={room}
                      setShowToast={setShowToast}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                      apiToken={apiToken}
                      devMode={devMode}
                    />
                  )}
                  {/* Report Problem */}
                  {isRPDrawerOpen && (
                    <ReportProblemDrawer
                      isRPDrawerOpen={isRPDrawerOpen}
                      setIsRPDrawerOpen={setIsRPDrawerOpen}
                      id={props.id}
                      clientPreferedServerId={props.clientPreferedServerId}
                      isWhiteboardOpen={isWhiteboardOpen}
                      localParticipant={room?.localParticipant}
                      setDrawerState={setDrawerState}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      devMode={devMode}
                      meetingFeatures={meetingFeatures}
                    />
                  )}
                  {/* Recording Consent Drawer */}
                  {showRecordingConsentDrawer && (
                    <RecordingConsentDrawer
                      showRecordingConsentDrawer={showRecordingConsentDrawer}
                      setShowRecordingConsentDrawer={setShowRecordingConsentDrawer}
                      participantConsent={participantConsent}
                      setDrawerState={setDrawerState}
                      room={room}
                      devMode={devMode}
                    />
                  )}
                </div>
              ) : (
                <div className="lk-grid-layout-wrapper">
                  <GridLayout tracks={tracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </GridLayout>
                  {/* Chat */}
                  {meetingFeatures?.conference_chat === 1 &&
                    (isMobileBrowser() ? (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        meetingFeatures={meetingFeatures}
                      />
                    ) : (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        meetingFeatures={meetingFeatures}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                      />
                    ))}
                  {/* {meetingFeatures?.conference_chat === 1 &&
                  isMobileBrowser() ? (
                    <div
                      className={`lk-chat ${
                        widgetState.showChat && isMobileBrowser()
                          ? "show-chat-mobile"
                          : ""
                      }`}
                      style={{
                        display: widgetState.showChat ? "grid" : "none",
                      }}
                    >
                      <Chat messageFormatter={formatChatMessageLinks} />
                    </div>
                  ) : (
                    <div
                      className={`lk-chat ${
                        widgetState.showChat ? "lk-show-chat" : ""
                      }`}
                      style={{
                        display: widgetState.showChat ? "flex" : "none",
                      }}
                    >
                      <Chat messageFormatter={formatChatMessageLinks} />
                    </div>
                  )} */}
                  {/* Live Captions */}
                  {meetingFeatures?.voice_transcription === 1 &&
                    isLiveCaptionsDrawerOpen && (
                      <LiveCaptionsDrawer
                        isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
                        setIsLiveCaptionsDrawerOpen={
                          setIsLiveCaptionsDrawerOpen
                        }
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        liveCaptionData={liveCaptionData}
                        meetingDetails={props.meetingDetails}
                        livecaptionsobject={liveCaptionsObject}
                        setlivecaptionsobject={setLiveCaptionsObject}
                        setfinalcaptions={setFinalCaptions}
                        finalcaptions={finalCaptions}
                        id={props.id}
                        isHost={props.isHost}
                        coHostToken={coHostToken}
                        isWhiteboardOpen={isWhiteboardOpen}
                        setDrawerState={setDrawerState}
                        translationDetails={translationDetails}
                        setTranslationDetails={setTranslationDetails}
                        finalTranslatedCaptions={finalTranslatedCaptions}
                        setFinalTranslatedCaptions={setFinalTranslatedCaptions}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        meetingFeatures={meetingFeatures}
                        apiToken={apiToken}
                        devMode={devMode}
                      />
                    )}
                  {/* Participant Drawer */}
                  {showParticipantsList &&
                    connected &&
                    (isMobileBrowser() ? (
                      <div className="side-drawer-mobile">
                        <ParticipantList
                          remoteParticipants={room.remoteParticipants}
                          localParticipant={room.localParticipant}
                          showParticipantsList={showParticipantsList}
                          setShowParticipantsList={setShowParticipantsList}
                          id={props.id}
                          isHost={props.isHost}
                          layoutContext={layoutContext}
                          isCoHost={isCoHost}
                          lobbyParticipants={lobbyParticipants}
                          setLobbyParticipants={setLobbyParticipants}
                          setRemoteRaisedHands={setRemoteRaisedHands}
                          coHostToken={coHostToken}
                          setDrawerState={setDrawerState}
                          breakoutRooms={breakoutRooms}
                          setShowRaiseHand={setShowRaiseHand}
                          currentRoomName={currentRoomName}
                          setToastNotification={setToastNotification}
                          setToastStatus={setToastStatus}
                          setShowToast={setShowToast}
                          setBreakoutRoomDuration={setBreakoutRoomDuration}
                          meetingDetails={props.meetingDetails}
                          setBreakoutRooms={setBreakoutRooms}
                          setRoomKeyCounter={setRoomKeyCounter}
                          isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                          setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                          apiToken={apiToken}
                          meetingFeatures={meetingFeatures}
                          setselectedprivatechatparticipant={
                            setSelectedPrivateChatParticipant
                          }
                          setprivatechatparticipants={
                            setPrivateChatParticipants
                          }
                          privatechatparticipants={privatechatparticipants}
                          setshowprivatechat={setShowPrivateChat}
                          forcemute={forceMute}
                          forcevideooff={forceVideoOff}
                          room={room}
                          isBreakoutRoom={isBreakoutRoom}
                          allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                          isWhiteboardOpen={isWhiteboardOpen}
                          isPinned={isPinned}
                          setIsPinned={setIsPinned}
                          devMode={devMode}
                        />
                      </div>
                    ) : (
                      <ParticipantList
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        showParticipantsList={showParticipantsList}
                        setShowParticipantsList={setShowParticipantsList}
                        id={props.id}
                        isHost={props.isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        lobbyParticipants={lobbyParticipants}
                        setLobbyParticipants={setLobbyParticipants}
                        setRemoteRaisedHands={setRemoteRaisedHands}
                        coHostToken={coHostToken}
                        setDrawerState={setDrawerState}
                        breakoutRooms={breakoutRooms}
                        setShowRaiseHand={setShowRaiseHand}
                        currentRoomName={currentRoomName}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setBreakoutRoomDuration={setBreakoutRoomDuration}
                        meetingDetails={props.meetingDetails}
                        setBreakoutRooms={setBreakoutRooms}
                        setRoomKeyCounter={setRoomKeyCounter}
                        isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                        apiToken={apiToken}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        setprivatechatparticipants={setPrivateChatParticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setShowPrivateChat}
                        forcemute={forceMute}
                        forcevideooff={forceVideoOff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        isWhiteboardOpen={isWhiteboardOpen}
                        isPinned={isPinned}
                        setIsPinned={setIsPinned}
                        devMode={devMode}
                      />
                    ))}
                  {/* Host Controls */}
                  {showHostControl && (
                    <HostControlDrawer
                      showHostControl={showHostControl}
                      setShowHostControl={setShowHostControl}
                      room={room}
                      setForceVideoOff={setForceVideoOff}
                      forceVideoOff={forceVideoOff}
                      setForceMute={setForceMute}
                      forceMute={forceMute}
                      setCanDownloadChatAttachment={
                        setCanDownloadChatAttachment
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      setAllowLiveCollabWhiteBoard={
                        setAllowLiveCollabWhiteBoard
                      }
                      allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                    />
                  )}
                  {meetingFeatures?.breakout_room === 1 && showBreakoutRoom && (
                    <BreakoutRoomDrawer
                      showBreakoutRoom={showBreakoutRoom}
                      setShowBreakoutRoom={setShowBreakoutRoom}
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      breakoutRooms={breakoutRooms}
                      setBreakoutRooms={setBreakoutRooms}
                      id={props.id}
                      setRoomKeyCounter={setRoomKeyCounter}
                      roomKeyCounter={roomKeyCounter}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      breakoutRoomDuration={breakoutRoomDuration}
                      meetingDetails={props.meetingDetails}
                      apiToken={apiToken}
                      coHostToken={coHostToken}
                      isHost={props.isHost}
                      setDrawerState={setDrawerState}
                      setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                      isWhiteboardOpen={isWhiteboardOpen}
                      devMode={devMode}
                    />
                  )}
                  {isVBDrawerOpen && (
                    <VirtualBackgroundDrawer
                      isVBDrawerOpen={isVBDrawerOpen}
                      setIsVBDrawerOpen={setIsVBDrawerOpen}
                      room={room}
                      setShowToast={setShowToast}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                      apiToken={apiToken}
                      devMode={devMode}
                    />
                  )}
                  {/* Report Problem */}
                  {isRPDrawerOpen && (
                    <ReportProblemDrawer
                      isRPDrawerOpen={isRPDrawerOpen}
                      setIsRPDrawerOpen={setIsRPDrawerOpen}
                      id={props.id}
                      clientPreferedServerId={props.clientPreferedServerId}
                      isWhiteboardOpen={isWhiteboardOpen}
                      localParticipant={room?.localParticipant}
                      setDrawerState={setDrawerState}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      devMode={devMode}
                      meetingFeatures={meetingFeatures}
                    />
                  )}
                  {/* Recording Consent Drawer */}
                  {showRecordingConsentDrawer && (
                    <RecordingConsentDrawer
                      showRecordingConsentDrawer={showRecordingConsentDrawer}
                      setShowRecordingConsentDrawer={setShowRecordingConsentDrawer}
                      participantConsent={participantConsent}
                      setDrawerState={setDrawerState}
                      room={room}
                      devMode={devMode}
                    />
                  )}
                </div>
              )
            ) : (
              <div className="lk-focus-layout-wrapper">
                <FocusLayoutContainer>
                  <CarouselLayout tracks={carouselTracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </CarouselLayout>
                  {focusTrack && (
                    <ParticipantTile
                      trackRef={focusTrack}
                      showEmojiReaction={showEmojiReaction}
                      setShowEmojiReaction={setShowEmojiReaction}
                      showRaiseHand={showRaiseHand}
                      remoteRaisedHands={remoteRaisedHands}
                      remoteEmojiReactions={remoteEmojiReactions}
                      setRemoteEmojiReactions={setRemoteEmojiReactions}
                    />
                  )}
                </FocusLayoutContainer>
                {meetingFeatures?.conference_chat === 1 &&
                  (isMobileBrowser() ? (
                    <ChatsDrawer
                      messageFormatter={formatChatMessageLinks}
                      privatechatparticipants={privatechatparticipants}
                      setprivatechatparticipants={setPrivateChatParticipants}
                      selectedprivatechatparticipant={
                        selectedPrivateChatParticipant
                      }
                      setselectedprivatechatparticipant={
                        setSelectedPrivateChatParticipant
                      }
                      showPrivateChat={showPrivateChat}
                      setShowPrivateChat={setShowPrivateChat}
                      localparticipant={room.localParticipant}
                      privatechatmessages={privatechatmessages}
                      setprivatechatmessages={setPrivateChatMessages}
                      newmessagerender={newMessageRender}
                      show={showChatDrawer}
                      setShow={setShowChatDrawer}
                      setPrivateChatUnreadMessagesCount={
                        setPrivateChatUnreadMessagesCount
                      }
                      privateChatUnreadMessagesCount={
                        privateChatUnreadMessagesCount
                      }
                      setPublicChatUnreadMessagesCount={
                        setPublicChatUnreadMessagesCount
                      }
                      publicChatUnreadMessagesCount={
                        publicChatUnreadMessagesCount
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      chatMessages={publicChatMessages}
                      setpublicchatmessage={setPublicChatMessages}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      meetingFeatures={meetingFeatures}
                    />
                  ) : (
                    <ChatsDrawer
                      messageFormatter={formatChatMessageLinks}
                      privatechatparticipants={privatechatparticipants}
                      setprivatechatparticipants={setPrivateChatParticipants}
                      selectedprivatechatparticipant={
                        selectedPrivateChatParticipant
                      }
                      setselectedprivatechatparticipant={
                        setSelectedPrivateChatParticipant
                      }
                      showPrivateChat={showPrivateChat}
                      setShowPrivateChat={setShowPrivateChat}
                      localparticipant={room.localParticipant}
                      privatechatmessages={privatechatmessages}
                      setprivatechatmessages={setPrivateChatMessages}
                      newmessagerender={newMessageRender}
                      show={showChatDrawer}
                      setShow={setShowChatDrawer}
                      setPrivateChatUnreadMessagesCount={
                        setPrivateChatUnreadMessagesCount
                      }
                      privateChatUnreadMessagesCount={
                        privateChatUnreadMessagesCount
                      }
                      setPublicChatUnreadMessagesCount={
                        setPublicChatUnreadMessagesCount
                      }
                      publicChatUnreadMessagesCount={
                        publicChatUnreadMessagesCount
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      chatMessages={publicChatMessages}
                      setpublicchatmessage={setPublicChatMessages}
                      meetingFeatures={meetingFeatures}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                    />
                  ))}
                {/* {meetingFeatures?.conference_chat === 1 && isMobileBrowser() ? (
                  <div
                    className={`lk-chat ${
                      widgetState.showChat && isMobileBrowser()
                        ? "show-chat-mobile"
                        : ""
                    }`}
                    style={{ display: widgetState.showChat ? "grid" : "none" }}
                  >
                    <Chat messageFormatter={formatChatMessageLinks} />
                  </div>
                ) : (
                  <div
                    className={`lk-chat ${
                      widgetState.showChat ? "lk-show-chat" : ""
                    }`}
                    style={{ display: widgetState.showChat ? "flex" : "none" }}
                  >
                    <Chat messageFormatter={formatChatMessageLinks} />
                  </div>
                )} */}
                {showParticipantsList &&
                  connected &&
                  (isMobileBrowser() ? (
                    <div className="side-drawer-mobile">
                      <ParticipantList
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        showParticipantsList={showParticipantsList}
                        setShowParticipantsList={setShowParticipantsList}
                        id={props.id}
                        isHost={props.isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        lobbyParticipants={lobbyParticipants}
                        setLobbyParticipants={setLobbyParticipants}
                        setRemoteRaisedHands={setRemoteRaisedHands}
                        coHostToken={coHostToken}
                        setDrawerState={setDrawerState}
                        breakoutRooms={breakoutRooms}
                        setShowRaiseHand={setShowRaiseHand}
                        currentRoomName={currentRoomName}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setBreakoutRoomDuration={setBreakoutRoomDuration}
                        meetingDetails={props.meetingDetails}
                        setBreakoutRooms={setBreakoutRooms}
                        setRoomKeyCounter={setRoomKeyCounter}
                        isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                        apiToken={apiToken}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        setprivatechatparticipants={setPrivateChatParticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setShowPrivateChat}
                        forcemute={forceMute}
                        forcevideooff={forceVideoOff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        isWhiteboardOpen={isWhiteboardOpen}
                        isPinned={isPinned}
                        setIsPinned={setIsPinned}
                        devMode={devMode}
                      />
                    </div>
                  ) : (
                    <ParticipantList
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      showParticipantsList={showParticipantsList}
                      setShowParticipantsList={setShowParticipantsList}
                      id={props.id}
                      isHost={props.isHost}
                      layoutContext={layoutContext}
                      isCoHost={isCoHost}
                      lobbyParticipants={lobbyParticipants}
                      setLobbyParticipants={setLobbyParticipants}
                      setRemoteRaisedHands={setRemoteRaisedHands}
                      coHostToken={coHostToken}
                      setDrawerState={setDrawerState}
                      breakoutRooms={breakoutRooms}
                      setShowRaiseHand={setShowRaiseHand}
                      currentRoomName={currentRoomName}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      setBreakoutRoomDuration={setBreakoutRoomDuration}
                      meetingDetails={props.meetingDetails}
                      setBreakoutRooms={setBreakoutRooms}
                      setRoomKeyCounter={setRoomKeyCounter}
                      isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                      setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                      apiToken={apiToken}
                      meetingFeatures={meetingFeatures}
                      setselectedprivatechatparticipant={
                        setSelectedPrivateChatParticipant
                      }
                      setprivatechatparticipants={setPrivateChatParticipants}
                      privatechatparticipants={privatechatparticipants}
                      setshowprivatechat={setShowPrivateChat}
                      forcemute={forceMute}
                      forcevideooff={forceVideoOff}
                      room={room}
                      isBreakoutRoom={isBreakoutRoom}
                      allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                      isWhiteboardOpen={isWhiteboardOpen}
                      isPinned={isPinned}
                      setIsPinned={setIsPinned}
                      devMode={devMode}
                    />
                  ))}
                {showHostControl && (
                  <HostControlDrawer
                    showHostControl={showHostControl}
                    setShowHostControl={setShowHostControl}
                    room={room}
                    setForceVideoOff={setForceVideoOff}
                    forceVideoOff={forceVideoOff}
                    setForceMute={setForceMute}
                    forceMute={forceMute}
                    setCanDownloadChatAttachment={setCanDownloadChatAttachment}
                    canDownloadChatAttachment={canDownloadChatAttachment}
                    setAllowLiveCollabWhiteBoard={setAllowLiveCollabWhiteBoard}
                    allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                    isWhiteboardOpen={isWhiteboardOpen}
                    setDrawerState={setDrawerState}
                  />
                )}
                {meetingFeatures?.voice_transcription === 1 &&
                  isLiveCaptionsDrawerOpen && (
                    <LiveCaptionsDrawer
                      isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
                      setIsLiveCaptionsDrawerOpen={setIsLiveCaptionsDrawerOpen}
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      liveCaptionData={liveCaptionData}
                      meetingDetails={props.meetingDetails}
                      livecaptionsobject={liveCaptionsObject}
                      setlivecaptionsobject={setLiveCaptionsObject}
                      setfinalcaptions={setFinalCaptions}
                      finalcaptions={finalCaptions}
                      id={props.id}
                      isHost={props.isHost}
                      coHostToken={coHostToken}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                      translationDetails={translationDetails}
                      setTranslationDetails={setTranslationDetails}
                      finalTranslatedCaptions={finalTranslatedCaptions}
                      setFinalTranslatedCaptions={setFinalTranslatedCaptions}
                      meetingFeatures={meetingFeatures}
                      apiToken={apiToken}
                      devMode={devMode}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                    />
                  )}
                {meetingFeatures?.breakout_room === 1 && showBreakoutRoom && (
                  <BreakoutRoomDrawer
                    showBreakoutRoom={showBreakoutRoom}
                    setShowBreakoutRoom={setShowBreakoutRoom}
                    remoteParticipants={room.remoteParticipants}
                    localParticipant={room.localParticipant}
                    breakoutRooms={breakoutRooms}
                    setBreakoutRooms={setBreakoutRooms}
                    id={props.id}
                    setRoomKeyCounter={setRoomKeyCounter}
                    roomKeyCounter={roomKeyCounter}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                    breakoutRoomDuration={breakoutRoomDuration}
                    meetingDetails={props.meetingDetails}
                    isHost={props.isHost}
                    apiToken={apiToken}
                    coHostToken={coHostToken}
                    setDrawerState={setDrawerState}
                    setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                    isWhiteboardOpen={isWhiteboardOpen}
                    devMode={devMode}
                  />
                )}
                {isVBDrawerOpen && (
                  <VirtualBackgroundDrawer
                    isVBDrawerOpen={isVBDrawerOpen}
                    setIsVBDrawerOpen={setIsVBDrawerOpen}
                    room={room}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                    isWhiteboardOpen={isWhiteboardOpen}
                    setDrawerState={setDrawerState}
                    apiToken={apiToken}
                    devMode={devMode}
                  />
                )}
                {isRPDrawerOpen && (
                  <ReportProblemDrawer
                    isRPDrawerOpen={isRPDrawerOpen}
                    setIsRPDrawerOpen={setIsRPDrawerOpen}
                    id={props.id}
                    clientPreferedServerId={props.clientPreferedServerId}
                    isWhiteboardOpen={isWhiteboardOpen}
                    localParticipant={room?.localParticipant}
                    setDrawerState={setDrawerState}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                    devMode={devMode}
                    meetingFeatures={meetingFeatures}
                  />
                )}
                {/* Recording Consent Drawer */}
                {showRecordingConsentDrawer && (
                    <RecordingConsentDrawer
                      showRecordingConsentDrawer={showRecordingConsentDrawer}
                      setShowRecordingConsentDrawer={setShowRecordingConsentDrawer}
                      participantConsent={participantConsent}
                      setDrawerState={setDrawerState}
                      room={room}
                      devMode={devMode}
                    />
                  )}
              </div>
            )}
            <MeetindEndedModal
              isMeetingEndedOpen={isDisconneted}
              setIsMeetingEndedOpen={setIsDisconnected}
            />
            <ControlBar
              controls={{
                chat: true,
                settings: !!SettingsComponent,
                info: true,
              }}
              showParticipantsList={showParticipantsList}
              setShowParticipantsList={setShowParticipantsList}
              showHostControl={showHostControl}
              setShowHostControl={setShowHostControl}
              showBreakoutRoom={showBreakoutRoom}
              setShowBreakoutRoom={setShowBreakoutRoom}
              showRaiseHand={showRaiseHand}
              setShowRaiseHand={setShowRaiseHand}
              showEmojiReaction={showEmojiReaction}
              setShowEmojiReaction={setShowEmojiReaction}
              widgetUpdate={widgetUpdate}
              room={room}
              id={props.id}
              isHost={props.isHost}
              maxWidth={props.maxWidth}
              maxHeight={props.maxHeight}
              meetingDetails={props.meetingDetails}
              showRecording={showRecording}
              isVBDrawerOpen={isVBDrawerOpen}
              setIsVBDrawerOpen={setIsVBDrawerOpen}
              isRPDrawerOpen={isRPDrawerOpen}
              isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
              setIsLiveCaptionsDrawerOpen={setIsLiveCaptionsDrawerOpen}
              setIsRPDrawerOpen={setIsRPDrawerOpen}
              setShowRecording={setShowRecording}
              isCoHost={isCoHost}
              isForceMuteAll={isForceMuteAll}
              isForceVideoOffAll={isForceVideoOffAll}
              coHostToken={coHostToken}
              setDrawerState={setDrawerState}
              drawerState={drawerState}
              isBreakoutRoom={isBreakoutRoom}
              remoteParticipants={room.remoteParticipants}
              localParticipant={room.localParticipant}
              meetingFeatures={meetingFeatures}
              showChatDrawer={showChatDrawer}
              setShowChatDrawer={setShowChatDrawer}
              privateChatUnreadMessagesCount={privateChatUnreadMessagesCount}
              publicChatUnreadMessagesCount={publicChatUnreadMessagesCount}
              showlivecaptionsicon={liveCaptionsObject.showIcon}
              isWebinarMode={isWebinarMode}
              isPIPEnabled={isPIPEnabled}
              setIsPIPEnabled={setIsPIPEnabled}
              sipData={sipData}
              isWhiteboardOpen={isWhiteboardOpen}
              setIsWhiteboardOpen={setIsWhiteboardOpen}
              screenShareSources={screenShareSources}
              isElectronApp={isElectronApp}
              isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
              setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
              whiteboardSceneData={whiteboardSceneData}
              setWhiteboardSceneData={setWhiteboardSceneData}
              whiteBoardId={whiteboardId}
              setWhiteboardId={setWhiteboardId}
              setScreenShareDisplayId={setScreenShareDisplayId}
              isScreenShareEnabled={isScreenShareEnabled}
              setIsScreenShareEnabled={setIsScreenShareEnabled}
              screenShareMode={screenShareMode}
              setScreenShareMode={setScreenShareMode}
              onScreenShareChange={onScreenShareChange}
              setToastNotification={setToastNotification}
              setToastStatus={setToastStatus}
              setShowToast={setShowToast}
              isSelfVideoMirrored={isSelfVideoMirrored}
              setIsSelfVideoMirrored={setIsSelfVideoMirrored}
              // deviceIdAudio={deviceIdAudio}
              // setDeviceIdAudio={setDeviceIdAudio}
              // deviceIdVideo={deviceIdVideo}
              // setDeviceIdVideo={setDeviceIdVideo}
              isRecordingLoading={isRecordingLoading}
              setIsRecordingLoading={setIsRecordingLoading}
              setParticipantConsent={setParticipantConsent}
              setShowRecordingConsentDrawer={setShowRecordingConsentDrawer}
              setShowRecordingConsentIcon={setShowRecordingConsentIcon}
              showRecordingConsentIcon={showRecordingConsentIcon}
              showRecordingConsentDrawer={showRecordingConsentDrawer}
              apiToken={apiToken}
              websiteBaseUrl={websiteBaseUrl}
              inviteurl={inviteurl}
              devMode={devMode}
            />
          </div>
          {/* <Chat
            style={{ display: widgetState.showChat ? "grid" : "none" }}
            messageFormatter={formatChatMessageLinks}
          /> */}
          {/* {showParticipantsList && connected && (
            <ParticipantList
              remoteParticipants={room.remoteParticipants}
              localParticipant={room.localParticipant}
              showParticipantsList={showParticipantsList}
              setShowParticipantsList={setShowParticipantsList}
              id={props.id}
              isHost={props.isHost}
            />
          )} */}
          <NotificationModal
            notificationVisible={notificationVisible}
            setNotificationVisible={setNotificationVisible}
            content={notificationContent}
            action={notificationAction}
            room={room}
          />
          <RecordingConsentModal
            isRecordingConsentModalOpen={isRecordingConsentModalOpen}
            setIsRecordingConsentModalOpen={setIsRecordingConsentModalOpen}
            remoteParticipant={room.remoteParticipants}
            room={room}
            id={props.id}
            setToastNotification={setToastNotification}
            setToastStatus={setToastStatus}
            setShowToast={setShowToast}
            showDeniedModal={showDeniedModal}
            setShowDeniedModal={setShowDeniedModal}
            participantConsent={participantConsent}
            setParticipantConsent={setParticipantConsent}
            devMode={devMode}
          />
          {/* <TimerNotification
            timerSeconds={timerSeconds}
            setTimerSeconds={setTimerSeconds}
          /> */}
        </LayoutContextProvider>
      )}
      <RoomAudioRenderer />
      {showToast && (
        // <Toast>
        //   <>{toastNotification}</>
        // </Toast>
        <StatusNotification
          message={toastNotification}
          status={toastStatus}
          // show={showToast}
          setShow={setShowToast}
        />
      )}
      {endMeetingNotificaton && props?.meetingDetails?.meeting_config?.auto_meeting_end === 1 && props.isHost && (
          <Toast
           className="end-meeting-notification"
          >
            <p className="end-meeting-notification-message" >Your meeting is about to end in next {autoMeetingEndTime.diff(moment(),"minutes")} minutes.</p>
            <p>Do you want to extend it further?</p>
            <div>
            <Button
              type="primary"
              onClick={()=>{
                extendMeeting();
              }}
            >
              Extend for 10 min
            </Button>
            <Button onClick={()=> {
              setEndMeetingNotification(false);
              dismissed.current = true;
            }}>
              Ignore
            </Button>
            </div>
          </Toast>
        )}
      <ConnectionStateToast state={connectionState} />
    </div>
  );
}
