import {
  createLocalAudioTrack,
  createLocalTracks,
  createLocalVideoTrack,
  facingModeFromLocalTrack,
  Track,
  VideoPresets,
  Mutex,
} from "livekit-client";
import * as React from "react";
import {
  MediaDeviceMenu,
  // TrackToggle,
  useMediaDevices,
  usePersistentUserChoices,
} from "@livekit/components-react";
import { onDeviceError } from "../utils/helper";
import { ParticipantPlaceholder } from "../assets/images/index";
import { TrackToggle } from "../components/TrackToggle";
import "../styles/Prejoin.scss";

/**
 * @public
 */
export function usePreviewTracks(options, onError, setToastMessage, setToastStatus, setShowToast) {
  const [tracks, setTracks] = React.useState();

  const trackLock = React.useMemo(() => new Mutex(), []);

  React.useEffect(() => {
    let needsCleanup = false;
    let localTracks = [];
    trackLock.lock().then(async (unlock) => {
      try {
        if (options.audio || options.video) {
          localTracks = await createLocalTracks(options);

          if (needsCleanup) {
            localTracks.forEach((tr) => tr.stop());
          } else {
            setTracks(localTracks);
          }
        }
      } catch (e) {
        if (onError && e instanceof Error) {
          onError(e);
        } else {
          setToastMessage("Permission denied for accessing audio/video devices.");
          setToastStatus("error");
          setShowToast(true);
          console.log(e);
        }
      } finally {
        unlock();
      }
    });

    return () => {
      needsCleanup = true;
      localTracks.forEach((track) => {
        track.stop();
      });
    };
  }, [JSON.stringify(options), onError, trackLock]);

  return tracks;
}

/** @public */
export function usePreviewDevice(enabled, deviceId, deviceKind) {
  const [deviceError, setDeviceError] = React.useState(null);
  const [isCreatingTrack, setIsCreatingTrack] = React.useState(false);

  const devices = useMediaDevices({ kind: deviceKind });
  const [selectedDevice, setSelectedDevice] = React.useState(undefined);

  const [localTrack, setLocalTrack] = React.useState();
  const [localDeviceId, setLocalDeviceId] = React.useState(deviceId);

  React.useEffect(() => {
    setLocalDeviceId(deviceId);
  }, [deviceId]);

  const prevDeviceId = React.useRef(localDeviceId); // Moved prevDeviceId declaration here

  const createTrack = async (id, kind) => {
    try {
      const track =
        kind === "videoinput"
          ? await createLocalVideoTrack({
              deviceId: id,
              resolution: VideoPresets.h720.resolution,
            })
          : await createLocalAudioTrack({ deviceId: id });

      const newDeviceId = await track.getDeviceId();
      if (newDeviceId && id !== newDeviceId) {
        prevDeviceId.current = newDeviceId;
        setLocalDeviceId(newDeviceId);
      }
      setLocalTrack(track);
    } catch (e) {
      if (e instanceof Error) {
        setDeviceError(e);
      }
    }
  };

  const switchDevice = async (track, id) => {
    await track.setDeviceId(id);
    prevDeviceId.current = id;
  };

  React.useEffect(() => {
    if (enabled && !localTrack && !deviceError && !isCreatingTrack) {
      setIsCreatingTrack(true);
      createTrack(localDeviceId, deviceKind).finally(() => {
        setIsCreatingTrack(false);
      });
    }
  }, [
    enabled,
    localTrack,
    deviceError,
    isCreatingTrack,
    localDeviceId,
    deviceKind,
  ]);

  React.useEffect(() => {
    if (!localTrack) {
      return;
    }
    if (!enabled) {
      localTrack.mute().then(() => console.log(localTrack.mediaStreamTrack));
    } else if (
      selectedDevice?.deviceId &&
      prevDeviceId.current !== selectedDevice?.deviceId
    ) {
      switchDevice(localTrack, selectedDevice.deviceId);
    } else {
      localTrack.unmute();
    }
  }, [localTrack, selectedDevice, enabled, deviceKind]);

  React.useEffect(() => {
    return () => {
      if (localTrack) {
        localTrack.stop();
        localTrack.mute();
      }
    };
  }, [localTrack]);

  React.useEffect(() => {
    setSelectedDevice(devices?.find((dev) => dev.deviceId === localDeviceId));
  }, [localDeviceId, devices]);

  return {
    selectedDevice,
    localTrack,
    deviceError,
  };
}

export function PreJoinAudioVideo({
  defaults = {},
  onValidate,
  onError,
  username,
  setIsValid,
  setUserChoices,
  persistUserChoices = true,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  setToastMessage,
  setToastStatus,
  setShowToast,
  ...htmlProps
}) {
  const partialDefaults = {
    ...(defaults.audioDeviceId !== undefined && {
      audioDeviceId: defaults.audioDeviceId,
    }),
    ...(defaults.videoDeviceId !== undefined && {
      videoDeviceId: defaults.videoDeviceId,
    }),
    ...(defaults.audioEnabled !== undefined && {
      audioEnabled: defaults.audioEnabled,
    }),
    ...(defaults.videoEnabled !== undefined && {
      videoEnabled: defaults.videoEnabled,
    }),
    ...(defaults.username !== undefined && { username: defaults.username }),
  };

  const {
    userChoices: initialUserChoices,
    saveAudioInputDeviceId,
    saveAudioInputEnabled,
    saveVideoInputDeviceId,
    saveVideoInputEnabled,
    saveUsername,
  } = usePersistentUserChoices({
    defaults: partialDefaults,
    preventSave: !persistUserChoices,
    preventLoad: !persistUserChoices,
  });

  const [audioEnabled, setAudioEnabled] = React.useState(
    initialUserChoices.audioEnabled
  );
  const [videoEnabled, setVideoEnabled] = React.useState(
    initialUserChoices.videoEnabled
  );
  const [audioDeviceId, setAudioDeviceId] = React.useState(
    initialUserChoices.audioDeviceId
  );
  const [videoDeviceId, setVideoDeviceId] = React.useState(
    initialUserChoices.videoDeviceId
  );

  React.useEffect(() => {
    saveAudioInputEnabled(audioEnabled);
  }, [audioEnabled, saveAudioInputEnabled]);
  React.useEffect(() => {
    saveVideoInputEnabled(videoEnabled);
  }, [videoEnabled, saveVideoInputEnabled]);
  React.useEffect(() => {
    saveAudioInputDeviceId(audioDeviceId);
  }, [audioDeviceId, saveAudioInputDeviceId]);
  React.useEffect(() => {
    saveVideoInputDeviceId(videoDeviceId);
  }, [videoDeviceId, saveVideoInputDeviceId]);
  React.useEffect(() => {
    saveUsername(username);
  }, [username, saveUsername]);

  const tracks = usePreviewTracks(
    {
      audio: audioEnabled
        ? { deviceId: initialUserChoices.audioDeviceId }
        : false,
      video: videoEnabled
        ? { deviceId: initialUserChoices.videoDeviceId }
        : false,
    },
    onError,
    setToastMessage,
    setToastStatus,
    setShowToast
  );

  const videoEl = React.useRef(null);

  const videoTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Video)[0],
    [tracks]
  );

  const trackFacingMode = React.useMemo(() => {
    if (videoTrack) {
      const { facingMode } = facingModeFromLocalTrack(videoTrack);
      return facingMode;
    } else {
      return "undefined";
    }
  }, [videoTrack]);

  const audioTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Audio)[0],
    [tracks]
  );

  React.useEffect(() => {
    if (videoEl.current && videoTrack) {
      videoTrack.unmute();
      videoTrack.attach(videoEl.current);
    }

    return () => {
      videoTrack?.detach();
    };
  }, [videoTrack]);

  const handleValidation = React.useCallback(
    (values) => {
      if (typeof onValidate === "function") {
        return onValidate(values);
      } else {
        return values.username !== "";
      }
    },
    [onValidate]
  );

  React.useEffect(() => {
    const newUserChoices = {
      username,
      videoEnabled,
      videoDeviceId,
      audioEnabled,
      audioDeviceId,
    };
    setUserChoices(newUserChoices);
    setIsValid(handleValidation(newUserChoices));
  }, [
    username,
    videoEnabled,
    videoDeviceId,
    audioEnabled,
    audioDeviceId,
    handleValidation,
  ]);

  return (
    <div className="lk-prejoin" {...htmlProps}>
      <div className="lk-video-container">
        {videoTrack && (
          <video
            ref={videoEl}
            width="1280"
            height="720"
            data-lk-facing-mode={trackFacingMode}
            style={{ transform: isSelfVideoMirrored ? "scaleX(-1)" : "scale(1)" }}
          >
            <track kind="captions" />
          </video>
        )}
        {(!videoTrack || !videoEnabled) && (
          <div className="lk-camera-off-note">
            <ParticipantPlaceholder />
          </div>
        )}
      </div>
      <div
        className="lk-button-group-container"
        style={{ gap: "3rem", justifyContent: "center" }}
      >
        <div
          className="lk-button-group audio"
          style={{ width: "auto", justifyContent: "center" }}
        >
          <TrackToggle
            initialState={audioEnabled}
            source={Track.Source.Microphone}
            showIcon
            onChange={(enabled) => setAudioEnabled(enabled)}
            onDeviceError={onDeviceError}
            className="button-icon"
          />
          <div className="lk-button-group-menu">
            <MediaDeviceMenu
              initialSelection={audioDeviceId}
              kind="audioinput"
              disabled={!audioTrack}
              tracks={{ audioinput: audioTrack }}
              onActiveDeviceChange={(_, id) => setAudioDeviceId(id)}
            />
          </div>
        </div>
        <div
          className="lk-button-group video"
          style={{ width: "auto", justifyContent: "center" }}
        >
          <TrackToggle
            initialState={videoEnabled}
            source={Track.Source.Camera}
            showIcon
            onChange={(enabled) => setVideoEnabled(enabled)}
            onDeviceError={onDeviceError}
            className="button-icon"
          />
          <div className="lk-button-group-menu">
            <MediaDeviceMenu
              initialSelection={videoDeviceId}
              kind="videoinput"
              disabled={!videoTrack}
              tracks={{ videoinput: videoTrack }}
              onActiveDeviceChange={(_, id) => setVideoDeviceId(id)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
