import { ConnectionState } from 'livekit-client';
import React, { useEffect, useState } from 'react';
import { SpinnerIcon, Toast } from '@livekit/components-react';

export function ConnectionStateToast({ state }) {
  const [notification, setNotification] = useState(undefined);

  useEffect(() => {
    switch (state) {
      case ConnectionState.SignalReconnecting:
      case ConnectionState.Reconnecting:
        setNotification(
          <>
            <SpinnerIcon className="lk-spinner" /> Reconnecting
          </>
        );
        break;
      case ConnectionState.Connecting:
        setNotification(
          <>
            <SpinnerIcon className="lk-spinner" /> Connecting
          </>
        );
        break;
      case ConnectionState.Disconnected:
        setNotification(<>Disconnected</>);
        break;
      default:
        setNotification(undefined);
        break;
    }
  }, [state]);

  return notification ? <Toast className="lk-toast-connection-state">{notification}</Toast> : <></>;
}