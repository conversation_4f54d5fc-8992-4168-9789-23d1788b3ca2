/* eslint no-use-before-define: 0 */
import * as React from "react";
import { Track } from "livekit-client";
import { Avatar } from "antd";
import {
  isTrackReference,
  isTrackReferencePinned,
} from "@livekit/components-core";
import {
  ParticipantContext,
  TrackRefContext,
  useEnsureTrackRef,
  useFeatureContext,
  useMaybeLayoutContext,
  useMaybeParticipantContext,
  useMaybeTrackRefContext,
  ParticipantName,
  TrackMutedIndicator,
  ConnectionQualityIndicator,
  FocusToggle,
  // ParticipantPlaceholder,
  LockLockedIcon,
  ScreenShareIcon,
  VideoTrack,
  AudioTrack,
  useParticipantTile,
  useIsEncrypted,
} from "@livekit/components-react";
import { generateAvatar, parseMetadata } from "../utils/helper";
import { darkColors } from "../utils/constants";
import { RaiseHandOverlay } from "../components/raisehand/RaiseHandOverlay";
import { ReactionsOverlay } from "../components/reactions/ReactionOverlay";

export function ParticipantContextIfNeeded({ participant, children }) {
  const hasContext = !!useMaybeParticipantContext();
  return participant && !hasContext ? (
    <ParticipantContext.Provider value={participant}>
      {children}
    </ParticipantContext.Provider>
  ) : (
    <>{children}</>
  );
}

function TrackRefContextIfNeeded({ trackRef, children }) {
  const hasContext = !!useMaybeTrackRefContext();
  return trackRef && !hasContext ? (
    <TrackRefContext.Provider value={trackRef}>
      {children}
    </TrackRefContext.Provider>
  ) : (
    <>{children}</>
  );
}

export function ParticipantTile(
  {
    trackRef,
    showEmojiReaction,
    setShowEmojiReaction,
    showRaiseHand,
    remoteRaisedHands,
    remoteEmojiReactions,
    setRemoteEmojiReactions,
    children,
    onParticipantClick,
    disableSpeakingIndicator,
    ...htmlProps
  },
  ref
) {
  const trackReference = useEnsureTrackRef(trackRef);
  const { elementProps } = useParticipantTile({
    htmlProps,
    disableSpeakingIndicator,
    onParticipantClick,
    trackRef: trackReference,
  });
  const isEncrypted = useIsEncrypted(trackReference.participant);
  const layoutContext = useMaybeLayoutContext();
  const autoManageSubscription = useFeatureContext()?.autoSubscription;

  const handleSubscribe = React.useCallback(
    (subscribed) => {
      if (
        trackReference.source &&
        !subscribed &&
        layoutContext &&
        layoutContext.pin.dispatch &&
        isTrackReferencePinned(trackReference, layoutContext.pin.state)
      ) {
        layoutContext.pin.dispatch({ msg: "clear_pin" });
      }
    },
    [trackReference, layoutContext]
  );

  const [avatarName, setAvatarName] = React.useState("");
  const [participantMetadata, setParticipantMetadata] = React.useState({});

  React.useEffect(() => {
    if (trackReference.participant) {
      setAvatarName(
        trackReference.participant.name
          ? generateAvatar(trackReference.participant.name)
          : generateAvatar(trackReference.participant.identity)
      );
      setParticipantMetadata(parseMetadata(trackReference.participant.metadata));
    } else {
      setAvatarName("YO");
      setParticipantMetadata({});
    }
  }, [trackReference.participant.name, trackReference.participant.metadata]);

  const [randomColor, setRandomColor] = React.useState("#fd4563");

  React.useEffect(() => {
    const randomNumber = Math.floor(Math.random() * 50);
    setRandomColor(darkColors[randomNumber]);
  }, []);

  React.useEffect(() => {
    if (!trackReference || !trackReference.participant) return;
    const reaction = remoteEmojiReactions.get(
      trackReference.participant.identity
    );
    if (!reaction) return;
    const timeout = setTimeout(() => {
      setRemoteEmojiReactions((prev) => {
        const newMap = new Map(prev);
        newMap.delete(trackReference.participant.identity);
        return newMap;
      });
    }, 6000);
    return () => clearTimeout(timeout);
  }, [trackReference.participant, remoteEmojiReactions]);

  React.useEffect(() => {
    if (!trackReference.participant || !trackReference.participant.isLocal)
      return;
    if (!showEmojiReaction) return;
    trackReference.participant.setIsSpeaking(true);
    const timeout = setTimeout(() => {
      setShowEmojiReaction(null);
      trackReference.participant.setIsSpeaking(false);
    }, 6000);
    return () => clearTimeout(timeout);
  }, [trackReference.participant, showEmojiReaction]);

  React.useEffect(() => {
    if (!trackReference.participant || !trackReference.participant.isLocal)
      return;
    if (!showEmojiReaction && !showRaiseHand) return;
    trackReference.participant.setIsSpeaking(true);
    const timeout = setTimeout(() => {
      trackReference.participant.setIsSpeaking(false);
    }, 500);
    return () => clearTimeout(timeout);
  }, [showEmojiReaction, showRaiseHand]);

  return (
    <div ref={ref} style={{ position: "relative" }} {...elementProps}>
      <TrackRefContextIfNeeded trackRef={trackReference}>
        <ParticipantContextIfNeeded participant={trackReference.participant}>
          {children ?? (
            <>
              {isTrackReference(trackReference) &&
              (trackReference.publication?.kind === "video" ||
                trackReference.source === Track.Source.Camera ||
                trackReference.source === Track.Source.ScreenShare) ? (
                <VideoTrack
                  trackRef={trackReference}
                  onSubscriptionStatusChanged={handleSubscribe}
                  manageSubscription={autoManageSubscription}
                  style={{
                    transform:
                      trackReference.source === Track.Source.Camera &&
                      trackReference.publication?.track?.mediaStreamTrack?.getSettings()
                        .facingMode !== "environment"
                        ? "rotateY(0deg)"
                        : "",
                    filter:
                      trackReference.source === Track.Source.ScreenShare &&
                      trackReference.participant.isLocal
                        ? "blur(70px)"
                        : "",
                  }}
                />
              ) : (
                isTrackReference(trackReference) && (
                  <AudioTrack
                    trackRef={trackReference}
                    onSubscriptionStatusChanged={handleSubscribe}
                  />
                )
              )}
              {trackReference.source === Track.Source.ScreenShare &&
                trackReference.participant.isLocal && (
                  <div className="lk-screen-share-overlay">
                    <span>
                      You are sharing your screen.
                    </span>
                  </div>
                )}
              {showEmojiReaction && trackReference.participant.isLocal && (
                <ReactionsOverlay reaction={showEmojiReaction} />
              )}
              {showRaiseHand && trackReference.participant.isLocal && (
                <RaiseHandOverlay />
              )}
              {remoteRaisedHands.get(trackReference.participant.identity) && (
                <RaiseHandOverlay />
              )}
              {remoteEmojiReactions.get(
                trackReference.participant.identity
              ) && (
                <ReactionsOverlay
                  reaction={remoteEmojiReactions.get(
                    trackReference.participant.identity
                  )}
                />
              )}
              <div className="lk-participant-placeholder">
                {/* <ParticipantPlaceholder /> */}
                <Avatar
                  className="avatar-style primary-font"
                  style={{ backgroundColor: randomColor }}
                >
                  {avatarName}
                </Avatar>
              </div>
              <div className="lk-participant-metadata">
                <div className="lk-participant-metadata-item">
                  {trackReference.source === Track.Source.Camera ? (
                    <>
                      {isEncrypted && (
                        <LockLockedIcon style={{ marginRight: "0.25rem" }} />
                      )}
                      <TrackMutedIndicator
                        trackRef={{
                          participant: trackReference.participant,
                          source: Track.Source.Microphone,
                        }}
                        show={"muted"}
                      />
                      <ParticipantName />
                    </>
                  ) : (
                    <>
                      <ScreenShareIcon style={{ marginRight: "0.25rem" }} />
                      <ParticipantName>&apos;s screen</ParticipantName>
                    </>
                  )}
                </div>
                <ConnectionQualityIndicator className="lk-participant-metadata-item" />
              </div>
            </>
          )}
          <FocusToggle trackRef={trackReference} />
        </ParticipantContextIfNeeded>
      </TrackRefContextIfNeeded>
    </div>
  );
}

ParticipantTile = React.forwardRef(ParticipantTile); // eslint-disable-line
